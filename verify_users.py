#!/usr/bin/env python3
"""
Quick verification script to check if demo users exist and can authenticate
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import authenticate
from user.models import User

def verify_users():
    print("🔍 Verifying Demo Users for Change Password Testing")
    print("=" * 60)
    
    test_users = [
        {'username': 'coach1', 'password': 'password123', 'expected_role': 'coach'},
        {'username': 'cashier1', 'password': 'password123', 'expected_role': 'cashier'},
        {'username': 'cashier2', 'password': 'password123', 'expected_role': 'cashier'},
        {'username': 'cleaner1', 'password': 'password123', 'expected_role': 'cleaner'},
        {'username': 'developer', 'password': None, 'expected_role': 'admin'},  # Will check if exists
    ]
    
    results = []
    
    for user_data in test_users:
        username = user_data['username']
        password = user_data['password']
        expected_role = user_data['expected_role']
        
        print(f"\n🧪 Testing: {username}")
        
        # Check if user exists
        try:
            user = User.objects.get(username=username)
            print(f"   ✅ User exists: {user.name} ({user.role})")
            
            # Verify role
            if user.role == expected_role:
                print(f"   ✅ Role correct: {user.role}")
                role_correct = True
            else:
                print(f"   ⚠️  Role mismatch: expected {expected_role}, got {user.role}")
                role_correct = False
            
            # Test authentication (skip for admin as we don't know password)
            if password:
                auth_user = authenticate(username=username, password=password)
                if auth_user:
                    print(f"   ✅ Authentication successful")
                    auth_success = True
                else:
                    print(f"   ❌ Authentication failed")
                    auth_success = False
            else:
                print(f"   ⏭️  Skipping auth test (admin user)")
                auth_success = None
            
            results.append({
                'username': username,
                'exists': True,
                'role_correct': role_correct,
                'auth_success': auth_success,
                'actual_role': user.role,
                'is_active': user.is_active
            })
            
        except User.DoesNotExist:
            print(f"   ❌ User does not exist")
            results.append({
                'username': username,
                'exists': False,
                'role_correct': False,
                'auth_success': False,
                'actual_role': None,
                'is_active': False
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    total_users = len(results)
    existing_users = sum(1 for r in results if r['exists'])
    correct_roles = sum(1 for r in results if r['role_correct'])
    successful_auths = sum(1 for r in results if r['auth_success'] is True)
    
    print(f"Total Users Checked: {total_users}")
    print(f"Users Found: {existing_users}/{total_users}")
    print(f"Correct Roles: {correct_roles}/{total_users}")
    print(f"Successful Authentications: {successful_auths}/{total_users - 1}")  # -1 for admin
    
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅" if result['exists'] and result['role_correct'] else "❌"
        auth_status = "✅" if result['auth_success'] is True else ("⏭️" if result['auth_success'] is None else "❌")
        
        print(f"{status} {result['username']:>10}: "
              f"Exists: {'✅' if result['exists'] else '❌'} | "
              f"Role: {'✅' if result['role_correct'] else '❌'} | "
              f"Auth: {auth_status}")
    
    # Check if ready for testing
    ready_for_testing = all(r['exists'] and r['role_correct'] for r in results)
    auth_ready = all(r['auth_success'] is not False for r in results)
    
    print(f"\n🎯 TESTING READINESS:")
    if ready_for_testing and auth_ready:
        print("✅ All users are ready for change password testing!")
        print("✅ You can proceed with manual testing using the credentials:")
        print("   - coach1 / password123")
        print("   - cashier1 / password123") 
        print("   - cashier2 / password123")
        print("   - cleaner1 / password123")
        print("   - developer / (admin password)")
    else:
        print("❌ Some users are not ready for testing.")
        print("❌ Please run create_demo_data.py to create missing users.")
    
    return results

if __name__ == '__main__':
    verify_users()

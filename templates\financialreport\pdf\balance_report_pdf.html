{% load currency_filters %}
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>{{ title }}</title>
    <style>
        @page {
            size: a4 portrait;
            margin: 1cm;
        }
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap');

        body {
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: {{ template.text_color|default:"#333333" }};
            background-color: {{ template.background_color|default:"#ffffff" }};
        }

        /* Professional report container */
        .report-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        {% if is_preview %}
        /* Preview mode styles */
        .preview-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #16a34a;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }

        .preview-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            z-index: 1000;
        }

        .preview-button {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #16a34a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .preview-button.secondary {
            background-color: #6c757d;
        }

        body {
            margin-top: 50px;
            margin-bottom: 60px;
        }
        {% endif %}
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid {{ template.accent_color|default:"#16a34a" }};
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
            color: {{ template.header_color|default:"#16a34a" }};
            font-weight: 700;
        }

        .header h2 {
            font-size: 18px;
            margin: 10px 0;
            color: {{ template.accent_color|default:"#16a34a" }};
            font-weight: 600;
        }

        .header p {
            font-size: 14px;
            margin: 5px 0;
            color: {{ template.text_color|default:"#333333" }};
        }
        .summary {
            margin-bottom: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: space-between;
        }
        .summary-card {
            border: 1px solid {{ template.accent_color|default:"#16a34a" }};
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: rgba(0, 0, 0, 0.02);
            text-align: center;
            width: 30%;
        }

        .summary-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: {{ template.header_color|default:"#16a34a" }};
        }

        .summary-value {
            font-size: 22px;
            font-weight: bold;
        }

        .income-value {
            color: #2563eb;
        }
        .expense-value {
            color: #e11d48;
        }
        .balance-value-positive {
            color: #16a34a;
        }
        .balance-value-negative {
            color: #eab308;
        }

        /* Dual currency styles for PDF */
        .currency-primary {
            font-weight: bold;
            display: block;
        }
        .currency-secondary {
            font-size: 10px;
            color: #666;
            font-weight: normal;
            display: block;
            margin-top: 2px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
        }
        th {
            background-color: {{ template.table_header_color|default:"#f8f9fa" }};
            color: {{ template.text_color|default:"#333333" }};
            font-weight: 600;
            text-align: left;
            padding: 10px;
            border-bottom: 2px solid {{ template.accent_color|default:"#16a34a" }};
            font-size: 12px;
        }

        td {
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            color: {{ template.header_color|default:"#16a34a" }};
            padding: 8px 0;
            border-bottom: 1px solid {{ template.accent_color|default:"#16a34a" }};
        }

        .footer {
            text-align: center;
            font-size: 11px;
            color: {{ template.text_color|default:"#333333" }};
            margin-top: 40px;
            border-top: 1px solid #e0e0e0;
            padding-top: 15px;
            {% if not template.show_footer %}display: none;{% endif %}
        }
        .income-section {
            background-color: #f0f9ff;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .expense-section {
            background-color: #fff1f2;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    {% if is_preview %}
    <div class="preview-banner">
        Preview Mode - {{ format_type|upper }} Format
    </div>
    {% endif %}

    <div class="report-container">
        <div class="header">
            {% if template.show_logo %}
            <h1>LEGEND FITNESS</h1>
            {% endif %}
            <h2>{{ title }}</h2>
            <p>Period: {{ date_range }}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <div class="summary-title">Total Income</div>
                <div class="summary-value income-value">
                    <span class="currency-primary">{{ income_total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_total|convert_khr_to_usd|format_usd }}</span>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-title">Total Expenses</div>
                <div class="summary-value expense-value">
                    <span class="currency-primary">{{ expense_total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_total|convert_khr_to_usd|format_usd }}</span>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-title">Net Balance</div>
                <div class="summary-value {% if balance >= 0 %}balance-value-positive{% else %}balance-value-negative{% endif %}">
                    <span class="currency-primary">{{ balance|format_khr }}</span>
                    <span class="currency-secondary">{{ balance|convert_khr_to_usd|format_usd }}</span>
                </div>
            </div>
        </div>

        <!-- Payment Method Breakdown -->
        <div class="payment-method-breakdown">
            <h3>Payment Method Breakdown</h3>
            <table style="margin-bottom: 20px;">
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Cash Payments</th>
                        <th>Bank Transfer</th>
                        <th>Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background-color: #f0f9ff;">
                        <td style="font-weight: bold; color: #2563eb;">Income</td>
                        <td class="currency-primary">{{ income_payment_methods.cash|format_khr }}</td>
                        <td class="currency-primary">{{ income_payment_methods.bank|format_khr }}</td>
                        <td class="currency-primary">{{ income_total|format_khr }}</td>
                    </tr>
                    <tr style="background-color: #fff1f2;">
                        <td style="font-weight: bold; color: #e11d48;">Expenses</td>
                        <td class="currency-primary">{{ expense_payment_methods.cash|format_khr }}</td>
                        <td class="currency-primary">{{ expense_payment_methods.bank|format_khr }}</td>
                        <td class="currency-primary">{{ expense_total|format_khr }}</td>
                    </tr>
                    <tr style="font-weight: bold; {% if balance >= 0 %}background-color: #f0fdf4;{% else %}background-color: #fefce8;{% endif %}">
                        <td>Net Balance</td>
                        <td class="currency-primary">{{ balance_payment_methods.cash|format_khr }}</td>
                        <td class="currency-primary">{{ balance_payment_methods.bank|format_khr }}</td>
                        <td class="currency-primary">{{ balance|format_khr }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Balance Summary Table -->
    <div class="section-title">Balance Summary</div>
    <table>
        <thead><tr>
                <th>Category</th>
                <th>Amount (KHR / USD)</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            <!-- Income Section -->
            <tr style="background-color: #f0f9ff;">
                <td colspan="3" style="font-weight: bold; color: #2563eb;">Income Sources</td>
            </tr>
            <tr>
                <td>Membership Fees</td>
                <td>
                    <span class="currency-primary">{{ income_sources.membership.total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_sources.membership.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ income_sources.membership.percentage }}%</td>
            </tr>
            <tr>
                <td>Product Sales</td>
                <td>
                    <span class="currency-primary">{{ income_sources.product_sales.total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_sources.product_sales.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ income_sources.product_sales.percentage }}%</td>
            </tr>
            <tr>
                <td>Pay-per-visit</td>
                <td>
                    <span class="currency-primary">{{ income_sources.paypervisit.total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_sources.paypervisit.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ income_sources.paypervisit.percentage }}%</td>
            </tr>
            <tr>
                <td>Deposits</td>
                <td>
                    <span class="currency-primary">{{ income_sources.deposits.total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_sources.deposits.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ income_sources.deposits.percentage }}%</td>
            </tr>
            <tr style="font-weight: bold; background-color: #f0f9ff;">
                <td>Total Income</td>
                <td>
                    <span class="currency-primary">{{ income_total|format_khr }}</span>
                    <span class="currency-secondary">{{ income_total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>100%</td>
            </tr>

            <!-- Expense Section -->
            <tr style="background-color: #fff1f2;">
                <td colspan="3" style="font-weight: bold; color: #e11d48;">Expense Categories</td>
            </tr>
            <tr>
                <td>Salaries</td>
                <td>
                    <span class="currency-primary">{{ expense_categories.salaries.total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_categories.salaries.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ expense_categories.salaries.percentage }}%</td>
            </tr>
            <tr>
                <td>Bills</td>
                <td>
                    <span class="currency-primary">{{ expense_categories.bills.total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_categories.bills.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ expense_categories.bills.percentage }}%</td>
            </tr>
            <tr>
                <td>Purchases</td>
                <td>
                    <span class="currency-primary">{{ expense_categories.purchases.total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_categories.purchases.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ expense_categories.purchases.percentage }}%</td>
            </tr>
            <tr>
                <td>Withdrawals</td>
                <td>
                    <span class="currency-primary">{{ expense_categories.withdrawals.total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_categories.withdrawals.total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ expense_categories.withdrawals.percentage }}%</td>
            </tr>
            <tr style="font-weight: bold; background-color: #fff1f2;">
                <td>Total Expenses</td>
                <td>
                    <span class="currency-primary">{{ expense_total|format_khr }}</span>
                    <span class="currency-secondary">{{ expense_total|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>100%</td>
            </tr>

            <!-- Balance Row -->
            <tr style="font-weight: bold; {% if balance >= 0 %}background-color: #f0fdf4;{% else %}background-color: #fefce8;{% endif %}">
                <td>Net Balance</td>
                <td>
                    <span class="currency-primary">{{ balance|format_khr }}</span>
                    <span class="currency-secondary">{{ balance|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ balance_percentage }}%</td>
            </tr>
        </tbody>
    </table>

    {% if template.show_footer %}
    <div class="footer">
        <p>Generated on {{ now|date:"d-M-Y H:i" }} | {{ template.footer_text|default:"Legend Fitness Club" }}</p>
    </div>
    {% endif %}

    {% if is_preview %}
    <div class="preview-actions">
        {% if format_type == 'pdf' %}
        <a href="{% url 'financialreport:export_pdf' 'balance' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download PDF
        </a>
        {% elif format_type == 'csv' %}
        <a href="{% url 'financialreport:export_csv' 'balance' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download CSV
        </a>
        {% elif format_type == 'print' %}
        <button onclick="window.print()" class="preview-button"><i class="fa-solid fa-print"></i> Print Report</button>
        {% endif %}
        <a href="javascript:window.close()" class="preview-button secondary">
            <i class="fa-solid fa-times"></i> Close Preview
        </a>
    </div>
    {% endif %}
</body>
</html>

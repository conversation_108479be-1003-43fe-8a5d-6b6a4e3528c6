from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout as lg
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django.db import connection
from django.utils import translation
from django.utils.translation import gettext as _
from django.http import HttpResponseRedirect, JsonResponse
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt

from django.utils import timezone
from django.db.models import Sum
from datetime import datetime
from user.models import User, MetaData
from members.models import Member
from .utils import generate_unique_transaction_id
from .decorators import module_permission_required
from .logging_utils import log_auth_action, get_client_ip
# Create your views here.

def index(request):
    # Redirect the user if they are authenticated
    if request.user.is_authenticated:
        # If the user has no role, set a default role based on is_superuser
        if not request.user.role:
            if request.user.is_superuser:
                request.user.role = 'admin'
                request.user.name = request.user.name or 'Administrator'
                request.user.save()
                return redirect('/adminDashboard/')
            # Legacy handling for users with is_manager=True but no role
            elif request.user.is_manager:
                request.user.role = 'admin'  # Convert to admin instead of manager
                request.user.name = request.user.name or request.user.username.capitalize()
                request.user.save()
                return redirect('/adminDashboard/')

        # Redirect based on role
        if request.user.role == 'admin':
            return redirect('/adminDashboard/')
        else:
            return redirect('/employeeDashboard/')
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")

        # Enhanced error handling for specific login failures
        if username and password:
            # First check if the username exists
            try:
                User.objects.get(username=username)
                # Username exists, now try to authenticate
                user = authenticate(request, username=username, password=password)
                if user is not None:
                    # Check if user is active
                    if user.status:
                        next_url = request.GET.get('next')
                        if next_url:
                            success_url = next_url
                        else:
                            success_url = "/employeeDashboard/"
                        login(request, user)

                        # Log successful login
                        log_auth_action(
                            request=request,
                            action_type='login',
                            status='success',
                            description=f'User {user.username} logged in successfully'
                        )

                        # Redirect the user to suitable url
                        return redirect(success_url)
                    else:
                        messages.error(request, _("Your account has been deactivated. Please contact the administrator."))
                else:
                    # Username exists but authentication failed - wrong password
                    messages.error(request, _("Incorrect password"))

                    # Log failed login attempt
                    log_auth_action(
                        request=request,
                        action_type='failed_login',
                        status='failed',
                        description=f'Failed login attempt for username: {username} (incorrect password)'
                    )
            except User.DoesNotExist:
                # Username doesn't exist
                messages.error(request, _("User not found"))

                # Log failed login attempt
                log_auth_action(
                    request=request,
                    action_type='failed_login',
                    status='failed',
                    description=f'Failed login attempt for non-existent username: {username}'
                )
        else:
            # Missing username or password
            if not username:
                messages.error(request, _("Please enter your username"))
            elif not password:
                messages.error(request, _("Please enter your password"))
    return render(request,'auth/login.html')

@login_required
def logout(request):
    # Log logout before performing the logout
    log_auth_action(
        request=request,
        action_type='logout',
        status='success',
        description=f'User {request.user.username} logged out'
    )

    # Perform the logout
    lg(request)
    return redirect("/")

def set_language(request, language_code):
    """
    View to set the language preference and redirect back to the previous page
    """
    # Activate the language
    translation.activate(language_code)

    # Get the redirect URL (use the referrer or default to home)
    next_url = request.META.get('HTTP_REFERER', '/')

    # Create response
    response = HttpResponseRedirect(next_url)

    # Set the language cookie
    response.set_cookie(settings.LANGUAGE_COOKIE_NAME, language_code)

    return response

@login_required
def initialize(request):
    meta = MetaData.objects.last()
    if not meta:
        meta = MetaData.objects.create(funds=0)

    current_month = timezone.now().month

    # Get the month from the last checked date
    last_checked_month = meta.lastChecked.month if meta.lastChecked else 0

    # Only update dues if we're in a new month compared to the last check
    if last_checked_month != current_month:
        # Member
        members = Member.objects.all()
        for member in members:
            if member.status == True:
                # Check if the member is on a monthly payment plan or if their package has expired
                # We should only charge members who are on a monthly payment plan or whose package has expired

                # Get the current date
                current_date = timezone.now().date()

                # Skip members who have already paid for a package that hasn't expired yet
                if member.end_date > current_date:
                    # This member has an active package that they've already paid for
                    # Don't charge them again until their package expires
                    continue

                # If we get here, the member either has no package or their package has expired
                # So we should charge them the monthly fee
                if member.package:
                    bill = member.package.price_khr

                    bill_due = bill
                    if member.due_payment < 0:
                        bill_due += member.due_payment

                    # Create a transaction record for the membership fee
                    transaction_id = generate_unique_transaction_id()
                    # Transaction recording removed - transaction app has been removed
                    # Log the transaction instead
                    print(f"Monthly membership fee for {member.name} - {datetime.now().strftime('%B %Y')}: {bill}៛")

                    member.due_payment += bill
                    member.save()

        # Coach section removed

        # Employee - only update once per month
        employees = User.objects.all()
        for employee in employees:
            if employee.is_employee and employee.status == True:
                salary = employee.salary
                employee.due += salary
                employee.save()

        # Update the last checked date to the current date
        meta.lastChecked = timezone.now()
        meta.save()

    # Return without updating if we're still in the same month

@login_required
def adminDashboard(request):
    initialize(request)
    current_month_name = timezone.now().strftime('%B')

    members_count = Member.objects.all().count
    employees_count = User.objects.filter(is_employee=True).count

    # Get product count - import Product model if needed
    products_count = 0
    try:
        # Try to import the Product model dynamically
        from product.models import Product
        products_count = Product.objects.all().count()

        # Get top selling products
        top_products = Product.objects.all().order_by('-sold')[:5]
    except:
        top_products = []

    try:
        employees = User.objects.filter(due__gt=0, is_employee=True)
    except:
        employees = User.objects.filter(is_employee=True)

    members = Member.objects.all().order_by("-id")
    funds = MetaData.objects.last().funds

    # Get the current month and year
    current_month = datetime.now().month
    current_year = datetime.now().year

    # Transaction data removed - transaction app has been removed
    # Use empty data for charts
    labels_d = []
    data_d = []

    # Use empty data for expense chart
    labels_c = []
    data_c = []

    # Set breadcrumbs
    breadcrumbs = [
        {
            'title': 'Dashboard',
            'url': reverse('adminDashboard'),
            'icon': 'tachometer-alt'
        }
    ]

    context = {
        'members_count': members_count,
        'employees_count': employees_count,
        'products_count': products_count,
        'top_products': top_products,
        'employees': employees,
        'members': members,
        'labels_d': labels_d,
        'data_d': data_d,
        'labels_c': labels_c,
        'data_c': data_c,
        'current_month_name': current_month_name,
        'funds': funds,
        'breadcrumbs': breadcrumbs
    }
    return render(request, "dashboard/admin.html",context)


# API endpoints for dashboard report cards
@login_required
def api_paypervisit_report(request):
    """API endpoint for Pay-Per-Visit report data"""
    from paypervisit.models import PayPerVisit
    from django.db.models import Sum, Count
    from datetime import datetime, timedelta

    # Get time period from request (default to current month)
    period = request.GET.get('period', 'month')
    today = timezone.now().date()

    if period == 'day':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get pay-per-visit data
    ppv_data = PayPerVisit.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date
    ).aggregate(
        total_revenue=Sum('amount'),
        total_transactions=Count('id'),
        total_visitors=Sum('num_people')
    )

    # Calculate average per transaction
    avg_per_transaction = 0
    if ppv_data['total_transactions'] and ppv_data['total_transactions'] > 0:
        avg_per_transaction = ppv_data['total_revenue'] / ppv_data['total_transactions']

    return JsonResponse({
        'total_revenue': ppv_data['total_revenue'] or 0,
        'total_transactions': ppv_data['total_transactions'] or 0,
        'total_visitors': ppv_data['total_visitors'] or 0,
        'avg_per_transaction': int(avg_per_transaction),
        'period': period
    })


@login_required
def api_product_sales_report(request):
    """API endpoint for Product Sales report data"""
    from product.models import Sale, SaleItem, Product
    from django.db.models import Sum, Count
    from datetime import datetime, timedelta

    # Get time period from request (default to current month)
    period = request.GET.get('period', 'month')
    today = timezone.now().date()

    if period == 'day':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get sales data
    sales_data = Sale.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date
    ).aggregate(
        total_sales=Sum('total_amount'),
        total_transactions=Count('id')
    )

    # Get total items sold
    items_sold = SaleItem.objects.filter(
        sale__date__date__gte=start_date,
        sale__date__date__lte=end_date
    ).aggregate(
        total_items=Sum('quantity')
    )

    # Get top selling products
    top_products = SaleItem.objects.filter(
        sale__date__date__gte=start_date,
        sale__date__date__lte=end_date
    ).values('product__name').annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('price')
    ).order_by('-total_quantity')[:3]

    return JsonResponse({
        'total_sales': sales_data['total_sales'] or 0,
        'total_transactions': sales_data['total_transactions'] or 0,
        'total_items': items_sold['total_items'] or 0,
        'top_products': list(top_products),
        'period': period
    })


@login_required
def api_overview_report(request):
    """API endpoint for Overview report data"""
    from members.models import Member
    from payment.models import Payment
    from datetime import datetime, timedelta

    # Get time period from request (default to current month)
    period = request.GET.get('period', 'month')
    today = timezone.now().date()

    if period == 'day':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get member statistics
    total_members = Member.objects.count()
    active_members = Member.objects.filter(status=True).count()
    new_members = Member.objects.filter(
        start_date__gte=start_date,
        start_date__lte=end_date
    ).count()

    # Get members expiring soon (within 30 days)
    expiring_soon = Member.objects.filter(
        end_date__gte=today,
        end_date__lte=today + timedelta(days=30),
        status=True
    ).count()

    # Get recent payments
    recent_payments = Payment.objects.filter(
        payment_date__date__gte=start_date,
        payment_date__date__lte=end_date
    ).count()

    return JsonResponse({
        'total_members': total_members,
        'active_members': active_members,
        'new_members': new_members,
        'expiring_soon': expiring_soon,
        'recent_payments': recent_payments,
        'period': period
    })


@login_required
def api_income_report(request):
    """API endpoint for Income report data"""
    from payment.models import Payment
    from product.models import Sale
    from paypervisit.models import PayPerVisit
    from finance.models import Transaction
    from django.db.models import Sum
    from datetime import datetime, timedelta

    # Get time period from request (default to current month)
    period = request.GET.get('period', 'month')
    today = timezone.now().date()

    if period == 'day':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get income from different sources
    membership_income = Payment.objects.filter(
        payment_date__date__gte=start_date,
        payment_date__date__lte=end_date
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    product_income = Sale.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    paypervisit_income = PayPerVisit.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date
    ).aggregate(total=Sum('amount'))['total'] or 0

    deposit_income = Transaction.objects.filter(
        transaction_type='deposit',
        transaction_date__date__gte=start_date,
        transaction_date__date__lte=end_date,
        status='completed'
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    total_income = membership_income + product_income + paypervisit_income + deposit_income

    return JsonResponse({
        'total_income': total_income,
        'membership_income': membership_income,
        'product_income': product_income,
        'paypervisit_income': paypervisit_income,
        'deposit_income': deposit_income,
        'period': period
    })


@login_required
def api_expense_report(request):
    """API endpoint for Expense report data"""
    from finance.models import Transaction
    from payroll.models import SalaryPayment
    from billmanagement.models import Bill
    from product.models import Purchase
    from django.db.models import Sum
    from datetime import datetime, timedelta

    # Get time period from request (default to current month)
    period = request.GET.get('period', 'month')
    today = timezone.now().date()

    if period == 'day':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get expenses from different sources
    withdrawal_expenses = Transaction.objects.filter(
        transaction_type='withdrawal',
        transaction_date__date__gte=start_date,
        transaction_date__date__lte=end_date,
        status='completed'
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    salary_expenses = SalaryPayment.objects.filter(
        payment_date__date__gte=start_date,
        payment_date__date__lte=end_date,
        payment_status='paid'
    ).aggregate(total=Sum('final_pay'))['total'] or 0

    bill_expenses = Bill.objects.filter(
        payment_date__date__gte=start_date,
        payment_date__date__lte=end_date
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    # Add purchase expenses
    purchase_expenses = Purchase.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    total_expenses = withdrawal_expenses + salary_expenses + bill_expenses + purchase_expenses

    return JsonResponse({
        'total_expenses': total_expenses,
        'withdrawal_expenses': withdrawal_expenses,
        'salary_expenses': salary_expenses,
        'bill_expenses': bill_expenses,
        'purchase_expenses': purchase_expenses,
        'period': period
    })


@login_required
def api_balance_report(request):
    """API endpoint for Balance report data"""
    from user.models import MetaData

    # Get current balance from MetaData
    try:
        meta = MetaData.objects.last()
        current_balance = meta.funds if meta else 0
    except:
        current_balance = 0

    # Get income and expense data for trend calculation
    from django.http import HttpRequest

    # Create a mock request for the other API functions
    mock_request = HttpRequest()
    mock_request.user = request.user
    mock_request.GET = request.GET

    # Get income and expense data
    income_response = api_income_report(mock_request)
    expense_response = api_expense_report(mock_request)

    import json
    income_data = json.loads(income_response.content)
    expense_data = json.loads(expense_response.content)

    period_income = income_data['total_income']
    period_expenses = expense_data['total_expenses']
    period_balance = period_income - period_expenses

    # Calculate trend (positive if income > expenses, negative otherwise)
    trend = 'positive' if period_balance > 0 else 'negative' if period_balance < 0 else 'neutral'

    return JsonResponse({
        'current_balance': current_balance,
        'period_income': period_income,
        'period_expenses': period_expenses,
        'period_balance': period_balance,
        'trend': trend,
        'period': request.GET.get('period', 'month')
    })


def test_dashboard(request):
    """Test view for dashboard cards"""
    return render(request, "test-dashboard.html")


def test_period_selection(request):
    """Test view for period selection functionality"""
    return render(request, "test-period-selection.html")

@login_required
def employeeDashboard(request):
    initialize(request)
    # Redirect only users with admin role to the admin dashboard
    if request.user.role == 'admin':
        return redirect('/adminDashboard/')

    # If the user has no role, set a default role based on is_superuser
    if not request.user.role:
        if request.user.is_superuser:
            request.user.role = 'admin'
            request.user.name = request.user.name or 'Administrator'
            request.user.save()
            return redirect('/adminDashboard/')
        # Legacy handling for users with is_manager=True but no role
        elif request.user.is_manager:
            request.user.role = 'admin'  # Convert to admin instead of manager
            request.user.name = request.user.name or request.user.username.capitalize()
            request.user.save()
            return redirect('/adminDashboard/')

    user = request.user

    # Get the user's role for display in the template
    role = user.role

    # Get access permissions based on role and database permissions
    from settings.models import RolePermission

    # Initialize permissions dictionary with default values
    permissions = {
        'can_manage_members': False,
        'can_manage_payments': False,
        'can_manage_products': False,
        'can_manage_inventory': False,
        'can_manage_paypervisit': False,
        'can_manage_transactions': False,
        'can_manage_bills': False,
    }

    # Check database permissions for each module
    # Note: Removed admin bypass to respect granular permission settings

    # Check member management permission
    if RolePermission.has_permission(role, 'member', 'view'):
        permissions['can_manage_members'] = True

    # Check payment permission
    if RolePermission.has_permission(role, 'payment', 'view'):
        permissions['can_manage_payments'] = True

    # Check product permission
    if RolePermission.has_permission(role, 'product', 'view'):
        permissions['can_manage_products'] = True

    # Check purchase permission
    if RolePermission.has_permission(role, 'purchase', 'view'):
        permissions['can_manage_purchases'] = True

        # Check inventory permission
        if RolePermission.has_permission(role, 'inventory', 'view'):
            permissions['can_manage_inventory'] = True

        # Check pay-per-visit permission
        if RolePermission.has_permission(role, 'paypervisit', 'view'):
            permissions['can_manage_paypervisit'] = True

        # Check finance permission
        if RolePermission.has_permission(role, 'finance', 'view'):
            permissions['can_manage_transactions'] = True

        # Check bill permission
        if RolePermission.has_permission(role, 'bill', 'view'):
            permissions['can_manage_bills'] = True

    # Set breadcrumbs
    breadcrumbs = [
        {
            'title': 'Dashboard',
            'url': reverse('employeeDashboard'),
            'icon': 'tachometer-alt'
        }
    ]

    context = {
        'user': user,
        'role': role,
        'permissions': permissions,
        'breadcrumbs': breadcrumbs
    }
    return render(request, "dashboard/employee.html", context)



# Coach dashboard functionality removed

@login_required
@module_permission_required(module='settings', required_level='full')
def clean_all_data(request):
    """
    View for cleaning all data in the system
    Only accessible to admin users
    """
    # Initialize counters for display
    data_counts = {}

    # Get counts for all models
    try:
        from members.models import Member, Package
        data_counts['members'] = Member.objects.all().count()
        data_counts['packages'] = Package.objects.all().count()
    except:
        data_counts['members'] = 0
        data_counts['packages'] = 0

    try:
        from payment.models import Payment
        data_counts['payments'] = Payment.objects.all().count()
    except:
        data_counts['payments'] = 0

    try:
        from product.models import Product, Category, Supplier, Purchase, Sale
        data_counts['products'] = Product.objects.all().count()
        data_counts['categories'] = Category.objects.all().count()
        data_counts['suppliers'] = Supplier.objects.all().count()
        data_counts['purchases'] = Purchase.objects.all().count()
        data_counts['sales'] = Sale.objects.all().count()
    except:
        data_counts['products'] = 0
        data_counts['categories'] = 0
        data_counts['suppliers'] = 0
        data_counts['purchases'] = 0
        data_counts['sales'] = 0

    try:
        from paypervisit.models import PayPerVisit
        data_counts['paypervisits'] = PayPerVisit.objects.all().count()
    except:
        data_counts['paypervisits'] = 0

    try:
        from payroll.models import SalaryPayment
        data_counts['salary_payments'] = SalaryPayment.objects.all().count()
    except:
        data_counts['salary_payments'] = 0

    try:
        from billmanagement.models import Bill
        data_counts['bills'] = Bill.objects.all().count()
    except:
        data_counts['bills'] = 0

    # Count non-admin users
    data_counts['users'] = User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).count()

    # Process POST requests for data cleaning
    if request.method == "POST":
        action = request.POST.get("action")

        # Clean transaction tables (legacy)
        if action == "clean_transaction_table":
            try:
                with connection.cursor() as cursor:
                    # First delete records from transaction_payrollrecord
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    # Then delete records from transaction_transaction
                    cursor.execute("DELETE FROM transaction_transaction")
                messages.success(request, "Legacy transaction tables cleaned successfully")
            except Exception as e:
                messages.error(request, f"Error cleaning transaction tables: {str(e)}")

        # Clean members
        elif action == "clean_members":
            try:
                from members.models import Member
                count = Member.objects.all().count()
                Member.objects.all().delete()
                messages.success(request, f"All {count} members deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting members: {str(e)}")

        # Clean packages
        elif action == "clean_packages":
            try:
                from members.models import Package
                count = Package.objects.all().count()
                Package.objects.all().delete()
                messages.success(request, f"All {count} packages deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting packages: {str(e)}")

        # Clean payments
        elif action == "clean_payments":
            try:
                from payment.models import Payment
                count = Payment.objects.all().count()
                Payment.objects.all().delete()
                messages.success(request, f"All {count} payments deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting payments: {str(e)}")

        # Clean products
        elif action == "clean_products":
            try:
                from product.models import Product
                count = Product.objects.all().count()
                Product.objects.all().delete()
                messages.success(request, f"All {count} products deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting products: {str(e)}")

        # Clean categories
        elif action == "clean_categories":
            try:
                from product.models import Category
                count = Category.objects.all().count()
                Category.objects.all().delete()
                messages.success(request, f"All {count} categories deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting categories: {str(e)}")

        # Clean suppliers
        elif action == "clean_suppliers":
            try:
                from product.models import Supplier
                count = Supplier.objects.all().count()
                Supplier.objects.all().delete()
                messages.success(request, f"All {count} suppliers deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting suppliers: {str(e)}")

        # Clean purchases
        elif action == "clean_purchases":
            try:
                from product.models import Purchase, PurchaseItem
                purchase_count = Purchase.objects.all().count()
                item_count = PurchaseItem.objects.all().count()
                PurchaseItem.objects.all().delete()
                Purchase.objects.all().delete()
                messages.success(request, f"All {purchase_count} purchases and {item_count} purchase items deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting purchases: {str(e)}")

        # Clean sales
        elif action == "clean_sales":
            try:
                from product.models import Sale, SaleItem
                sale_count = Sale.objects.all().count()
                item_count = SaleItem.objects.all().count()
                SaleItem.objects.all().delete()
                Sale.objects.all().delete()
                messages.success(request, f"All {sale_count} sales and {item_count} sale items deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting sales: {str(e)}")

        # Clean pay-per-visit
        elif action == "clean_paypervisit":
            try:
                from paypervisit.models import PayPerVisit
                count = PayPerVisit.objects.all().count()
                PayPerVisit.objects.all().delete()
                messages.success(request, f"All {count} pay-per-visit records deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting pay-per-visit records: {str(e)}")

        # Clean salary payments
        elif action == "clean_salary_payments":
            try:
                from payroll.models import SalaryPayment
                count = SalaryPayment.objects.all().count()
                SalaryPayment.objects.all().delete()
                messages.success(request, f"All {count} salary payments deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting salary payments: {str(e)}")

        # Clean bills
        elif action == "clean_bills":
            try:
                from billmanagement.models import Bill
                count = Bill.objects.all().count()
                Bill.objects.all().delete()
                messages.success(request, f"All {count} bills deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting bills: {str(e)}")

        # Clean non-admin users
        elif action == "clean_users":
            try:
                # Only delete non-admin users
                count = User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).count()
                User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).delete()
                messages.success(request, f"All {count} non-admin users deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting users: {str(e)}")

        # Clean all data
        elif action == "clean_all":
            try:
                # First clean legacy transaction tables
                with connection.cursor() as cursor:
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    cursor.execute("DELETE FROM transaction_transaction")

                # Clean all data from all apps
                try:
                    from payment.models import Payment
                    payment_count = Payment.objects.all().count()
                    Payment.objects.all().delete()
                except:
                    payment_count = 0

                try:
                    from members.models import Member, Package
                    member_count = Member.objects.all().count()
                    package_count = Package.objects.all().count()
                    Member.objects.all().delete()
                    Package.objects.all().delete()
                except:
                    member_count = 0
                    package_count = 0

                try:
                    from product.models import Product, Category, Supplier, Purchase, PurchaseItem, Sale, SaleItem
                    product_count = Product.objects.all().count()
                    category_count = Category.objects.all().count()
                    supplier_count = Supplier.objects.all().count()
                    purchase_count = Purchase.objects.all().count()
                    sale_count = Sale.objects.all().count()

                    SaleItem.objects.all().delete()
                    Sale.objects.all().delete()
                    PurchaseItem.objects.all().delete()
                    Purchase.objects.all().delete()
                    Product.objects.all().delete()
                    Supplier.objects.all().delete()
                    Category.objects.all().delete()
                except:
                    product_count = 0
                    category_count = 0
                    supplier_count = 0
                    purchase_count = 0
                    sale_count = 0

                try:
                    from paypervisit.models import PayPerVisit
                    paypervisit_count = PayPerVisit.objects.all().count()
                    PayPerVisit.objects.all().delete()
                except:
                    paypervisit_count = 0

                try:
                    from payroll.models import SalaryPayment
                    salary_count = SalaryPayment.objects.all().count()
                    SalaryPayment.objects.all().delete()
                except:
                    salary_count = 0

                try:
                    from billmanagement.models import Bill
                    bill_count = Bill.objects.all().count()
                    Bill.objects.all().delete()
                except:
                    bill_count = 0

                # Only delete non-admin users
                user_count = User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).count()
                User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).delete()

                # Reset MetaData
                meta = MetaData.objects.last()
                if meta:
                    meta.funds = 0
                    meta.save()

                messages.success(request, f"All data cleaned successfully: {member_count} members, {package_count} packages, {payment_count} payments, {product_count} products, {purchase_count} purchases, {sale_count} sales, {paypervisit_count} pay-per-visit records, {salary_count} salary payments, {bill_count} bills, and {user_count} users deleted")
            except Exception as e:
                messages.error(request, f"Error cleaning all data: {str(e)}")

        # Refresh counts after cleaning
        try:
            from members.models import Member, Package
            data_counts['members'] = Member.objects.all().count()
            data_counts['packages'] = Package.objects.all().count()
        except:
            data_counts['members'] = 0
            data_counts['packages'] = 0

        try:
            from payment.models import Payment
            data_counts['payments'] = Payment.objects.all().count()
        except:
            data_counts['payments'] = 0

        try:
            from product.models import Product, Category, Supplier, Purchase, Sale
            data_counts['products'] = Product.objects.all().count()
            data_counts['categories'] = Category.objects.all().count()
            data_counts['suppliers'] = Supplier.objects.all().count()
            data_counts['purchases'] = Purchase.objects.all().count()
            data_counts['sales'] = Sale.objects.all().count()
        except:
            data_counts['products'] = 0
            data_counts['categories'] = 0
            data_counts['suppliers'] = 0
            data_counts['purchases'] = 0
            data_counts['sales'] = 0

        try:
            from paypervisit.models import PayPerVisit
            data_counts['paypervisits'] = PayPerVisit.objects.all().count()
        except:
            data_counts['paypervisits'] = 0

        try:
            from payroll.models import SalaryPayment
            data_counts['salary_payments'] = SalaryPayment.objects.all().count()
        except:
            data_counts['salary_payments'] = 0

        try:
            from billmanagement.models import Bill
            data_counts['bills'] = Bill.objects.all().count()
        except:
            data_counts['bills'] = 0

        # Count non-admin users
        data_counts['users'] = User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).count()

    # Set breadcrumbs
    breadcrumbs = [
        {
            'title': 'Dashboard',
            'url': reverse('adminDashboard'),
            'icon': 'tachometer-alt'
        },
        {
            'title': 'Clean All Data',
            'url': reverse('clean_all_data'),
            'icon': 'trash-alt'
        }
    ]

    context = {
        'title': 'Clean All Data',
        'subtitle': 'Clean data from all apps in the system',
        'data_counts': data_counts,
        'breadcrumbs': breadcrumbs
    }

    return render(request, 'core/clean_data.html', context)

@csrf_exempt
def test_currency_formatting(request):
    """
    View for testing currency formatting
    """
    return render(request, 'test_currency_formatting.html')

@csrf_exempt
def test_js_currency_formatting(request):
    """
    View for testing JavaScript currency formatting functions
    """
    return render(request, 'test_js_currency_formatting.html')


@login_required
def test_websocket(request):
    """Test page for WebSocket real-time permission updates"""
    return render(request, 'test_websocket.html')


@login_required
@csrf_exempt
def test_permission_update(request):
    """Test endpoint to trigger permission updates"""
    if request.method == 'POST':
        try:
            from settings.models import RolePermission
            from settings.cache_manager import PermissionCacheManager

            # Find a coach user for testing
            coach_user = User.objects.filter(role='coach').first()
            if not coach_user:
                return JsonResponse({'error': 'No coach user found'}, status=404)

            # Get or create permission for 'product' module
            permission, created = RolePermission.objects.get_or_create(
                role='coach',
                module='product',
                defaults={'permission_level': 'none'}
            )

            # Toggle permission
            old_level = permission.permission_level
            new_level = 'view' if old_level == 'none' else 'none'
            permission.permission_level = new_level
            permission.save()

            return JsonResponse({
                'success': True,
                'message': f'Permission updated from {old_level} to {new_level}',
                'old_permission': old_level,
                'new_permission': new_level,
                'affected_role': 'coach',
                'module': 'product'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Only POST method allowed'}, status=405)


@login_required
@csrf_exempt
def test_notification(request):
    """Test endpoint to trigger notifications"""
    if request.method == 'POST':
        try:
            import json
            from settings.cache_manager import PermissionCacheManager

            data = json.loads(request.body)
            message = data.get('message', 'Test notification')

            # Send notification to all connected users
            PermissionCacheManager.send_system_notification(
                message=message,
                notification_type='info'
            )

            return JsonResponse({
                'success': True,
                'message': 'Notification sent successfully',
                'notification_message': message
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Only POST method allowed'}, status=405)
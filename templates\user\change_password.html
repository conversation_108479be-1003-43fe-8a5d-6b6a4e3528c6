{% extends "../base.html" %}
{% load static %}
{% load i18n %}

{% block extra_css %}
<style>
    /* Change Password Page Specific Styles */
    .change-password-container {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: calc(100vh - 80px);
        padding: 1rem;
        /* Account for sidebar on desktop */
        margin-left: 16rem;
    }

    .change-password-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        max-width: 500px;
        margin: 0 auto;
    }

    .form-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .form-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("{% static 'img/gym-pattern.png' %}") center/cover;
        opacity: 0.1;
    }

    .form-header h1 {
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .form-header p {
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    .form-content {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-label i {
        color: #6b7280;
        margin-right: 0.5rem;
        width: 1rem;
        text-align: center;
    }

    .password-input-container {
        position: relative;
    }

    .form-input {
        width: 100%;
        padding: 0.875rem 3rem 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        background-color: #f9fafb;
        font-size: 1rem;
        transition: all 0.2s ease;
        min-height: 48px; /* Touch-friendly minimum height */
    }

    .form-input:focus {
        outline: none;
        border-color: #1e40af;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.15);
    }

    .form-input:hover {
        border-color: #9ca3af;
        background-color: white;
    }

    .password-toggle {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
        min-width: 44px; /* Touch-friendly minimum size */
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .password-toggle:hover {
        color: #1e40af;
        background-color: #f3f4f6;
    }

    .password-toggle:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2);
    }

    .help-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
    }

    .submit-button {
        width: 100%;
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        color: white;
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 48px; /* Touch-friendly minimum height */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .submit-button:hover {
        background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
        transform: translateY(-1px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .submit-button:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.2);
    }

    .submit-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }



    .navigation-buttons {
        display: flex;
        gap: 0.75rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        /* Ensure buttons are never cut off */
        min-width: 0;
        width: 100%;
        box-sizing: border-box;
    }

    .nav-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: #1e40af;
        color: white;
        text-decoration: none;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        min-height: 44px; /* Touch-friendly minimum height */
        /* Ensure buttons don't get cut off */
        flex-shrink: 0;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .nav-button:hover {
        background: #1e3a8a;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        color: white;
        text-decoration: none;
    }

    .nav-button.secondary {
        background: #059669;
    }

    .nav-button.secondary:hover {
        background: #047857;
    }

    /* Responsive Design */
    /* Mobile styles - no sidebar margin */
    @media (max-width: 640px) {
        .change-password-container {
            margin-left: 0 !important;
            padding: 0.5rem;
        }

        .form-content {
            padding: 1.5rem;
        }

        .form-header {
            padding: 1.5rem;
        }

        .form-header h1 {
            font-size: 1.5rem;
        }

        .navigation-buttons {
            flex-direction: column;
        }

        .nav-button {
            justify-content: center;
        }
    }

    /* Tablet styles - reduced sidebar margin */
    @media (min-width: 641px) and (max-width: 1023px) {
        .change-password-container {
            margin-left: 15rem;
            padding: 1.5rem;
        }

        .form-content {
            padding: 2.5rem;
        }

        .form-header {
            padding: 2.5rem;
        }
    }

    /* Desktop styles - full sidebar margin */
    @media (min-width: 1024px) {
        .change-password-container {
            margin-left: 16rem;
            padding: 2rem;
        }

        .form-content {
            padding: 3rem;
        }

        .form-header {
            padding: 3rem;
        }

        .change-password-card {
            max-width: 600px;
        }
    }

    /* Large desktop styles - extra spacing */
    @media (min-width: 1280px) {
        .change-password-container {
            padding: 2.5rem;
            /* Ensure adequate spacing from sidebar on very wide screens */
            margin-left: 17rem;
        }

        .navigation-buttons {
            margin-bottom: 2.5rem;
        }

        .change-password-card {
            max-width: 650px;
        }
    }

    /* Ultra-wide screen support */
    @media (min-width: 1920px) {
        .change-password-container {
            margin-left: 18rem;
            padding: 3rem;
        }

        .change-password-card {
            max-width: 700px;
        }
    }

    /* Error states */
    .form-input.error {
        border-color: #dc2626;
        background-color: #fef2f2;
    }

    .form-input.error:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.15);
    }

    .error-message {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Success states */
    .form-input.success {
        border-color: #10b981;
        background-color: #f0fdf4;
    }

    .success-message {
        color: #10b981;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
</style>
{% endblock %}

{% block body %}
<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full"></div>

<!-- Change Password Page -->
<div class="change-password-container">
    <div class="componentWrapper">
        <!-- Navigation Buttons -->
        <div class="navigation-buttons">
            <a href="/user/?tab=admins" class="nav-button">
                <i class="fas fa-arrow-left"></i>
                {% trans "Back to Admin Users List" %}
            </a>
        </div>

        <!-- Change Password Card -->
        <div class="change-password-card">
            <!-- Header -->
            <div class="form-header">
                <h1>{% trans "Change Password" %}</h1>
                <p>{% trans "Update your account password for better security" %}</p>
            </div>

            <!-- Form Content -->
            <div class="form-content">
                <form method="post" id="change-password-form" class="space-y-6" novalidate>
                    {% csrf_token %}

                    <!-- Current Password -->
                    <div class="form-group">
                        <label for="current_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            {% trans "Current Password" %}
                        </label>
                        <div class="password-input-container">
                            <input
                                type="password"
                                id="current_password"
                                name="current_password"
                                class="form-input"
                                required
                                autocomplete="current-password"
                                placeholder="{% trans 'Enter your current password' %}"
                                aria-describedby="current-password-help">
                            <button
                                type="button"
                                class="password-toggle"
                                onclick="togglePassword('current_password')"
                                aria-label="{% trans 'Toggle current password visibility' %}"
                                tabindex="-1">
                                <i id="current_password_icon" class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="current-password-help" class="help-text">
                            {% trans "Enter your current password to verify your identity" %}
                        </div>
                    </div>

                    <!-- New Password -->
                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-key"></i>
                            {% trans "New Password" %}
                        </label>
                        <div class="password-input-container">
                            <input
                                type="password"
                                id="new_password"
                                name="new_password"
                                class="form-input"
                                required
                                autocomplete="new-password"
                                placeholder="{% trans 'Enter your new password' %}"
                                aria-describedby="new-password-help"
                                minlength="8">
                            <button
                                type="button"
                                class="password-toggle"
                                onclick="togglePassword('new_password')"
                                aria-label="{% trans 'Toggle new password visibility' %}"
                                tabindex="-1">
                                <i id="new_password_icon" class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="new-password-help" class="help-text">
                            {% trans "Password must be at least 8 characters long and include a mix of letters, numbers, and special characters" %}
                        </div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-check-circle"></i>
                            {% trans "Confirm New Password" %}
                        </label>
                        <div class="password-input-container">
                            <input
                                type="password"
                                id="confirm_password"
                                name="confirm_password"
                                class="form-input"
                                required
                                autocomplete="new-password"
                                placeholder="{% trans 'Confirm your new password' %}"
                                aria-describedby="confirm-password-help"
                                minlength="8">
                            <button
                                type="button"
                                class="password-toggle"
                                onclick="togglePassword('confirm_password')"
                                aria-label="{% trans 'Toggle confirm password visibility' %}"
                                tabindex="-1">
                                <i id="confirm_password_icon" class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="confirm-password-help" class="help-text">
                            {% trans "Re-enter your new password to confirm" %}
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group">
                        <button type="submit" class="submit-button" id="submit-button">
                            <i class="fas fa-save"></i>
                            {% trans "Change Password" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Include Messages -->
{% include 'partials/messages.html' %}
{% endblock body %}

{% block js %}
<script>
    // Notification System
    function showNotification(type, title, message, duration = 5000) {
        const notificationContainer = document.getElementById('notification-container');
        if (!notificationContainer) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type} fixed top-4 right-4 max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;

        // Set border color based on type
        const borderColors = {
            'success': 'border-green-500',
            'error': 'border-red-500',
            'warning': 'border-yellow-500',
            'info': 'border-blue-500'
        };

        notification.classList.add(borderColors[type] || 'border-blue-500');

        // Set icon based on type
        const icons = {
            'success': 'fas fa-check-circle text-green-500',
            'error': 'fas fa-exclamation-circle text-red-500',
            'warning': 'fas fa-exclamation-triangle text-yellow-500',
            'info': 'fas fa-info-circle text-blue-500'
        };

        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="${icons[type] || icons.info}"></i>
                </div>
                <div class="ml-3 w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900">${title}</p>
                    <p class="mt-1 text-sm text-gray-500">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="closeNotification(this.parentElement.parentElement.parentElement)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        notificationContainer.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full', 'opacity-0');
        }, 100);

        // Auto remove after duration
        setTimeout(() => {
            closeNotification(notification);
        }, duration);
    }

    function closeNotification(notification) {
        if (notification) {
            notification.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    // Password Toggle Function
    function togglePassword(fieldId) {
        const passwordInput = document.getElementById(fieldId);
        const passwordIcon = document.getElementById(fieldId + '_icon');
        const toggleButton = passwordIcon.parentElement;

        if (passwordInput && passwordIcon) {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
                toggleButton.setAttribute('aria-label', '{% trans "Hide password" %}');
            } else {
                passwordInput.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
                toggleButton.setAttribute('aria-label', '{% trans "Show password" %}');
            }
        }
    }

    // Form Validation Functions
    function validatePassword(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        return {
            isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar,
            length: password.length >= minLength,
            upperCase: hasUpperCase,
            lowerCase: hasLowerCase,
            numbers: hasNumbers,
            specialChar: hasSpecialChar
        };
    }

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const container = field.parentElement;

        // Remove existing error message
        const existingError = container.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Add error class
        field.classList.add('error');
        field.classList.remove('success');

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        container.appendChild(errorDiv);
    }

    function showFieldSuccess(fieldId, message = '') {
        const field = document.getElementById(fieldId);
        const container = field.parentElement;

        // Remove existing messages
        const existingError = container.querySelector('.error-message');
        const existingSuccess = container.querySelector('.success-message');
        if (existingError) existingError.remove();
        if (existingSuccess) existingSuccess.remove();

        // Add success class
        field.classList.add('success');
        field.classList.remove('error');

        // Add success message if provided
        if (message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            container.appendChild(successDiv);
        }
    }

    function clearFieldValidation(fieldId) {
        const field = document.getElementById(fieldId);
        const container = field.parentElement;

        // Remove validation classes
        field.classList.remove('error', 'success');

        // Remove validation messages
        const errorMsg = container.querySelector('.error-message');
        const successMsg = container.querySelector('.success-message');
        if (errorMsg) errorMsg.remove();
        if (successMsg) successMsg.remove();
    }

    // Real-time validation
    function setupRealTimeValidation() {
        const newPasswordField = document.getElementById('new_password');
        const confirmPasswordField = document.getElementById('confirm_password');
        const currentPasswordField = document.getElementById('current_password');

        // New password validation
        newPasswordField.addEventListener('input', function() {
            const password = this.value;
            if (password.length === 0) {
                clearFieldValidation('new_password');
                return;
            }

            const validation = validatePassword(password);
            if (validation.isValid) {
                showFieldSuccess('new_password', '{% trans "Strong password!" %}');
            } else {
                let message = '{% trans "Password must include:" %}';
                if (!validation.length) message += ' {% trans "8+ characters" %}';
                if (!validation.upperCase) message += ' {% trans "uppercase" %}';
                if (!validation.lowerCase) message += ' {% trans "lowercase" %}';
                if (!validation.numbers) message += ' {% trans "numbers" %}';
                if (!validation.specialChar) message += ' {% trans "special characters" %}';
                showFieldError('new_password', message);
            }

            // Also validate confirm password if it has a value
            if (confirmPasswordField.value) {
                validateConfirmPassword();
            }
        });

        // Confirm password validation
        confirmPasswordField.addEventListener('input', validateConfirmPassword);

        function validateConfirmPassword() {
            const newPassword = newPasswordField.value;
            const confirmPassword = confirmPasswordField.value;

            if (confirmPassword.length === 0) {
                clearFieldValidation('confirm_password');
                return;
            }

            if (newPassword === confirmPassword) {
                showFieldSuccess('confirm_password', '{% trans "Passwords match!" %}');
            } else {
                showFieldError('confirm_password', '{% trans "Passwords do not match" %}');
            }
        }

        // Current password validation
        currentPasswordField.addEventListener('input', function() {
            if (this.value.length === 0) {
                clearFieldValidation('current_password');
            } else if (this.value.length > 0) {
                clearFieldValidation('current_password');
            }
        });
    }

    // Form submission handling
    function setupFormSubmission() {
        const form = document.getElementById('change-password-form');
        const submitButton = document.getElementById('submit-button');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            // Clear previous validation
            clearFieldValidation('current_password');
            clearFieldValidation('new_password');
            clearFieldValidation('confirm_password');

            let isValid = true;

            // Validate current password
            if (!currentPassword) {
                showFieldError('current_password', '{% trans "Current password is required" %}');
                isValid = false;
            }

            // Validate new password
            const passwordValidation = validatePassword(newPassword);
            if (!newPassword) {
                showFieldError('new_password', '{% trans "New password is required" %}');
                isValid = false;
            } else if (!passwordValidation.isValid) {
                showFieldError('new_password', '{% trans "Password does not meet security requirements" %}');
                isValid = false;
            }

            // Validate confirm password
            if (!confirmPassword) {
                showFieldError('confirm_password', '{% trans "Please confirm your new password" %}');
                isValid = false;
            } else if (newPassword !== confirmPassword) {
                showFieldError('confirm_password', '{% trans "Passwords do not match" %}');
                isValid = false;
            }

            // Check if new password is same as current password
            if (currentPassword === newPassword) {
                showFieldError('new_password', '{% trans "New password must be different from current password" %}');
                isValid = false;
            }

            if (isValid) {
                // Disable submit button and show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% trans "Changing Password..." %}';

                // Submit the form
                form.submit();
            } else {
                // Focus on first error field
                const firstError = form.querySelector('.form-input.error');
                if (firstError) {
                    firstError.focus();
                }

                showNotification('error', '{% trans "Validation Error" %}', '{% trans "Please fix the errors below and try again" %}', 5000);
            }
        });
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        setupRealTimeValidation();
        setupFormSubmission();
    });
</script>
{% endblock js %}

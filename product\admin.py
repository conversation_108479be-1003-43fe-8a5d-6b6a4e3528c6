from django.contrib import admin
from .models import Category, Supplier, Product, Purchase, PurchaseItem, Sale, SaleItem

# Register your models here.
@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'telegram', 'address')
    search_fields = ('name', 'phone', 'telegram', 'address', 'note')

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'sku', 'category', 'quantity', 'retail_price')
    list_filter = ('category', 'created_at')
    search_fields = ('name', 'sku', 'description')
    readonly_fields = ('created_at', 'updated_at')



@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    list_display = ('trxId', 'supplier', 'date', 'total_amount', 'payment_method', 'created_by')
    list_filter = ('date', 'supplier', 'payment_method')
    search_fields = ('trxId', 'supplier__name', 'notes')

@admin.register(PurchaseItem)
class PurchaseItemAdmin(admin.ModelAdmin):
    list_display = ('purchase', 'product', 'supplier', 'quantity', 'cost_price', 'total_cost')
    list_filter = ('purchase__date', 'supplier')
    search_fields = ('product__name', 'supplier__name')

@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('trxId', 'date', 'total_amount', 'payment_method', 'sold_by')
    list_filter = ('date', 'payment_method')
    search_fields = ('trxId', 'notes')

@admin.register(SaleItem)
class SaleItemAdmin(admin.ModelAdmin):
    list_display = ('sale', 'product', 'quantity', 'price', 'total_price')
    list_filter = ('sale__date',)
    search_fields = ('product__name',)

#!/usr/bin/env python3
"""
Test script for Change Password functionality across different user roles
Legend Fitness Club Gym Management System
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import authenticate
from django.urls import reverse

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from user.models import User

class ChangePasswordTestRunner:
    def __init__(self):
        self.client = Client()
        self.test_users = [
            {'username': 'admin', 'password': 'admin123', 'role': 'admin'},
            {'username': 'coach1', 'password': 'password123', 'role': 'coach'},
            {'username': 'cashier1', 'password': 'password123', 'role': 'cashier'},
            {'username': 'cashier2', 'password': 'password123', 'role': 'cashier'},
            {'username': 'cleaner1', 'password': 'password123', 'role': 'cleaner'},
            {'username': 'security1', 'password': 'password123', 'role': 'security'},
        ]
        self.results = []

    def test_user_access(self, user_data):
        """Test if user can access the change password page"""
        print(f"\n🧪 Testing {user_data['role'].upper()} - {user_data['username']}")
        
        # Login
        login_success = self.client.login(
            username=user_data['username'], 
            password=user_data['password']
        )
        
        if not login_success:
            return {
                'user': user_data['username'],
                'role': user_data['role'],
                'login': False,
                'access': False,
                'error': 'Login failed'
            }
        
        print(f"✅ Login successful for {user_data['username']}")
        
        # Test access to change password page
        try:
            response = self.client.get('/user/change-password/')
            access_success = response.status_code == 200
            
            if access_success:
                print(f"✅ Change password page accessible (Status: {response.status_code})")
            else:
                print(f"❌ Change password page not accessible (Status: {response.status_code})")
            
            return {
                'user': user_data['username'],
                'role': user_data['role'],
                'login': True,
                'access': access_success,
                'status_code': response.status_code,
                'error': None if access_success else f'HTTP {response.status_code}'
            }
            
        except Exception as e:
            print(f"❌ Error accessing change password page: {str(e)}")
            return {
                'user': user_data['username'],
                'role': user_data['role'],
                'login': True,
                'access': False,
                'error': str(e)
            }
        finally:
            # Logout
            self.client.logout()

    def test_password_change(self, user_data):
        """Test actual password change functionality"""
        print(f"\n🔐 Testing password change for {user_data['username']}")
        
        # Login
        login_success = self.client.login(
            username=user_data['username'], 
            password=user_data['password']
        )
        
        if not login_success:
            return False
        
        try:
            # Test password change
            new_password = f"newpass123_{user_data['role']}"
            response = self.client.post('/user/change-password/', {
                'current_password': user_data['password'],
                'new_password': new_password,
                'confirm_password': new_password,
            })
            
            # Check if password change was successful
            if response.status_code == 302:  # Redirect after successful change
                print(f"✅ Password change successful for {user_data['username']}")
                
                # Verify new password works
                self.client.logout()
                new_login = self.client.login(
                    username=user_data['username'], 
                    password=new_password
                )
                
                if new_login:
                    print(f"✅ New password verified for {user_data['username']}")
                    
                    # Change password back to original
                    self.client.post('/user/change-password/', {
                        'current_password': new_password,
                        'new_password': user_data['password'],
                        'confirm_password': user_data['password'],
                    })
                    print(f"✅ Password restored for {user_data['username']}")
                    return True
                else:
                    print(f"❌ New password verification failed for {user_data['username']}")
                    return False
            else:
                print(f"❌ Password change failed for {user_data['username']} (Status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ Error during password change for {user_data['username']}: {str(e)}")
            return False
        finally:
            self.client.logout()

    def run_all_tests(self):
        """Run all tests for all user roles"""
        print("🚀 Starting Change Password Functionality Tests")
        print("=" * 60)
        
        # Test 1: Access Tests
        print("\n📋 TEST 1: PAGE ACCESS VERIFICATION")
        print("-" * 40)
        
        for user_data in self.test_users:
            result = self.test_user_access(user_data)
            self.results.append(result)
        
        # Test 2: Password Change Tests
        print("\n📋 TEST 2: PASSWORD CHANGE FUNCTIONALITY")
        print("-" * 40)
        
        for user_data in self.test_users:
            if any(r['user'] == user_data['username'] and r['access'] for r in self.results):
                success = self.test_password_change(user_data)
                # Update results with password change status
                for result in self.results:
                    if result['user'] == user_data['username']:
                        result['password_change'] = success
                        break
        
        # Generate Report
        self.generate_report()

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 CHANGE PASSWORD FUNCTIONALITY TEST REPORT")
        print("=" * 60)
        
        print(f"\n📈 SUMMARY:")
        total_users = len(self.results)
        successful_logins = sum(1 for r in self.results if r['login'])
        successful_access = sum(1 for r in self.results if r.get('access', False))
        successful_changes = sum(1 for r in self.results if r.get('password_change', False))
        
        print(f"   Total Users Tested: {total_users}")
        print(f"   Successful Logins: {successful_logins}/{total_users}")
        print(f"   Page Access Success: {successful_access}/{total_users}")
        print(f"   Password Change Success: {successful_changes}/{total_users}")
        
        print(f"\n📋 DETAILED RESULTS:")
        print("-" * 60)
        
        for result in self.results:
            status_icon = "✅" if result.get('access', False) else "❌"
            change_icon = "✅" if result.get('password_change', False) else "❌"
            
            print(f"{status_icon} {result['role'].upper():>8} ({result['user']:>9}): "
                  f"Access: {'✅' if result.get('access', False) else '❌'} | "
                  f"Change: {change_icon}")
            
            if result.get('error'):
                print(f"         Error: {result['error']}")
        
        print("\n" + "=" * 60)
        
        # Overall status
        if successful_access == total_users and successful_changes == total_users:
            print("🎉 ALL TESTS PASSED! Change password functionality works for all user roles.")
        elif successful_access == total_users:
            print("⚠️  Page access works for all users, but some password changes failed.")
        else:
            print("❌ Some tests failed. Please check the detailed results above.")

if __name__ == '__main__':
    runner = ChangePasswordTestRunner()
    runner.run_all_tests()

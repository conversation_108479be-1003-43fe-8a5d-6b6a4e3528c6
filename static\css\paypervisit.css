/* Pay-per-visit POS Styles */

/* Notification System */
.notification-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 450px;
    width: 90%;
    margin-right: 0; /* Ensure no right margin */
}

.notification {
    padding: 1rem 1.25rem;
    border-radius: 0.75rem;
    background-color: #1e3a8a;
    color: white;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    overflow: hidden;
    animation: slideInRight 0.4s ease-out;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification.closing {
    animation: slideOutRight 0.4s ease-in forwards;
}

.notification-icon {
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
    opacity: 0.9;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: #ffffff; /* Bright white for better contrast */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); /* Add subtle text shadow for better readability */
}

.notification-message {
    font-size: 0.9rem;
    color: #f8fafc; /* Very light blue-white for better readability */
}

.notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0.25rem;
    margin-left: 0.5rem;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
}

.notification-close:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.3);
    width: 100%;
    transform-origin: left;
    animation: progress 5s linear forwards;
}

.notification.success {
    background-color: #065f46;
}

.notification.error {
    background-color: #b91c1c;
}

.notification.warning {
    background-color: #b45309;
}

@keyframes progress {
    0% { transform: scaleX(1); }
    100% { transform: scaleX(0); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOutDown {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(100%); opacity: 0; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Mobile responsive styles for notifications */
@media (max-width: 640px) {
    .notification-container {
        width: calc(100% - 2rem); /* Full width minus margins */
        max-width: 100%;
        right: 1rem;
    }
}

/* Responsive improvements for pay-per-visit interface */
@media (max-width: 768px) {
    /* Adjust quick selection buttons for mobile */
    .people-btn {
        font-size: 0.875rem;
        padding: 0.75rem 0.5rem;
    }

    /* Improve numpad button spacing on mobile */
    .numpad-btn {
        font-size: 1.125rem;
        padding: 0.75rem;
    }

    /* Adjust receipt preview for mobile */
    .receipt-preview {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    /* Improve form spacing on mobile */
    .payment-summary-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    /* Further adjustments for very small screens */
    .people-btn {
        font-size: 0.75rem;
        padding: 0.5rem 0.25rem;
    }

    .numpad-btn {
        font-size: 1rem;
        padding: 0.5rem;
    }

    /* Stack payment summary items vertically */
    .payment-details-stack {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Success Animation */
@keyframes successPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-animation {
    animation: successPulse 0.5s ease-in-out;
}

/* Receipt Preview Styles */
.receipt-preview {
    font-family: 'Courier New', monospace;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.receipt-preview:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px) rotate(1deg);
}

.receipt-preview::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 15px;
    right: 15px;
    height: 5px;
    background: repeating-linear-gradient(
        90deg,
        #e5e7eb,
        #e5e7eb 5px,
        transparent 5px,
        transparent 10px
    );
}

.receipt-preview::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 15px;
    right: 15px;
    height: 5px;
    background: repeating-linear-gradient(
        90deg,
        #e5e7eb,
        #e5e7eb 5px,
        transparent 5px,
        transparent 10px
    );
}

.receipt-paper {
    position: relative;
    background-color: white;
    background-image:
        linear-gradient(rgba(222, 226, 230, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(222, 226, 230, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: -1px -1px;
}

.receipt-header {
    text-align: center;
    margin-bottom: 1rem;
}

.receipt-logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1e40af;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.receipt-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #1f2937;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.receipt-subtitle {
    font-size: 0.8rem;
    color: #6b7280;
}

.receipt-divider {
    border-top: 1px dashed #d1d5db;
    margin: 0.75rem 0;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    color: #4b5563;
}

.receipt-total {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: #1f2937;
    padding-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
}

.receipt-footer {
    text-align: center;
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px dashed #d1d5db;
}

/* Print Button */
.print-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background-color: #1e40af;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
    width: 100%;
}

.print-button:hover {
    background-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(30, 64, 175, 0.3);
}

.print-button:active {
    transform: translateY(0);
}

.print-button-icon {
    margin-right: 0.5rem;
}

/* Numpad Button Styles */
.numpad-btn {
    transition: all 0.1s ease;
    min-height: 48px; /* Touch-friendly minimum height */
}

.numpad-btn:active {
    transform: scale(0.95);
    background-color: #e5e7eb;
}

.numpad-btn:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

/* Quick Selection Button Styles */
.people-btn {
    transition: all 0.2s ease;
    min-height: 48px; /* Touch-friendly minimum height */
}

.people-btn:active {
    transform: translateY(2px);
}

.people-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Touch-friendly improvements */
.touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Print Button Hover Effect */
#print-btn:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Process Button Hover Effect */
#process-btn:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Keyboard Shortcut Styles */
kbd {
    display: inline-block;
    padding: 2px 4px;
    font-size: 11px;
    line-height: 1;
    border-radius: 3px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

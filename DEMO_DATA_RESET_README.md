# Legend Fitness Club - Data Reset & Demo Creation System

## Overview

This document describes the comprehensive data reset and demo data creation system for the Legend Fitness Club gym management system. The system provides a safe way to clean all existing data while preserving specific admin users and create realistic demo data for testing and demonstration purposes.

## Features

### 🧹 **Data Cleaning**
- **Safe User Preservation**: Preserves specified admin users (default: 'developer', 'owner')
- **Comprehensive Cleaning**: Removes all data from all modules (members, products, payments, etc.)
- **Legacy Table Cleanup**: Cleans old transaction tables
- **Financial Reset**: Resets gym funds and financial metadata

### 🏗️ **Demo Data Creation**
- **Small-Scale Data**: Creates focused, manageable demo dataset
- **Authentic Cambodian Context**: Realistic names, addresses, phone numbers
- **Currency Compliance**: All KHR amounts in proper increments of 100
- **Complete Coverage**: Data across all system modules

## Demo Data Specifications

### 👥 **Users & Employees (4 total)**
- **Preserved**: developer, owner (existing admin accounts)
- **Created**: 4 new employees
  - 1 Coach: <PERSON><PERSON> (900,000 KHR salary)
  - 2 Cashiers: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (800,000 KHR each)
  - 1 Cleaner: <PERSON><PERSON> (600,000 KHR salary)

### 🏃 **Members (5 members)**
- Authentic Cambodian names and addresses
- Mix of membership statuses (active, expiring, pending)
- Realistic package assignments with weighted distribution
- Proper start/end dates and payment statuses

### 📦 **Membership Packages (4 packages)**
- **Basic**: 1 month, 120,000 KHR, peak hours only
- **Premium**: 3 months, 300,000 KHR, all hours + 1 PT session
- **VIP**: 6 months, 550,000 KHR, all hours + 3 PT sessions
- **Super VIP**: 12 months, 1,000,000 KHR, all hours + 10 PT sessions

### 🛍️ **Products (10 products)**
- **4 Categories**: Beverages, Snacks, Supplements, Merchandise
- **4 Suppliers**: Cambodian business context
- **Realistic Pricing**: All amounts in KHR increments of 100
- **Proper Inventory**: Box quantities and cost calculations

### 💰 **Financial Data**
- **2 Purchases**: Sample product purchases with multiple items
- **3 Sales**: Product sales transactions
- **3 Pay-per-visit**: Walk-in customer transactions
- **Member Payments**: Payments for all paid members
- **4 Salary Payments**: Monthly salary for all employees
- **3 Finance Transactions**: 2 deposits, 1 withdrawal
- **3 Bills**: Electricity, water, rent expenses

## Usage

### Basic Usage
```bash
# Show help and options
python manage.py reset_and_create_demo_data --help

# Preview what will be done (safe - no changes)
python manage.py reset_and_create_demo_data

# Execute the reset and demo creation
python manage.py reset_and_create_demo_data --confirm
```

### Advanced Usage
```bash
# Preserve specific users (default: developer, owner)
python manage.py reset_and_create_demo_data --confirm --preserve-users admin developer owner

# Preserve only one user
python manage.py reset_and_create_demo_data --confirm --preserve-users admin
```

## Safety Features

### 🔒 **Confirmation Required**
- Command requires `--confirm` flag to execute
- Shows preview of what will be preserved/deleted
- No accidental data loss

### 👤 **User Preservation**
- Preserves specified admin users by username
- Default preservation: 'developer', 'owner'
- Customizable via `--preserve-users` option

### 🔄 **Transaction Safety**
- Uses Django's `transaction.atomic()` for data integrity
- All-or-nothing execution (rollback on errors)
- Comprehensive error handling and reporting

## Currency Standards

### 💱 **Cambodian Riel (KHR)**
- All amounts in increments of 100 (e.g., 4,000៛, 120,000៛)
- No irregular amounts (no 125៛ or 4,236៛)
- Proper thousand separators in output

### 💵 **USD Conversion**
- Exchange rate: 4,000 KHR = 1 USD
- Automatic conversion for dual currency display
- Standard decimal precision for USD

## Output Example

```
=== Legend Fitness Club - Data Reset & Demo Creation ===

🧹 Cleaning existing data...
  - Deleted 15 Payment records
  - Deleted 8 Member records
  - Deleted 4 Package records
  - Deleted 12 Product records
  - Deleted 3 users (preserved: developer, owner)
  - Reset financial metadata
✅ Cleaned 42 total records

🏗️ Creating demo data...

👥 Creating employees...
  - Created coach: coach1 (Sokha Meas)
  - Created cashier: cashier1 (Bopha Chhay)
  - Created cashier: cashier2 (Sreypov Prak)
  - Created cleaner: cleaner1 (Srey Tep)
✅ Created 4 employees

📦 Creating membership packages...
  - Created package: Basic (1 months)
  - Created package: Premium (3 months)
  - Created package: VIP (6 months)
  - Created package: Super VIP (12 months)
✅ Created 4 packages

🏃 Creating members...
  - Created member: Dara Sok with Basic package (Active)
  - Created member: Bopha Meas with Premium package (Active)
  - Created member: Sokha Chhay with VIP package (Expiring Soon)
  - Created member: Srey Prak with Basic package (Active)
  - Created member: Veasna Tep with Premium package (Pending)
✅ Created 5 members

🛍️ Creating products and suppliers...
  - Created product: Mineral Water (BEV-001)
  - Created product: Sports Drink (BEV-002)
  - Created product: Protein Bar (SNK-001)
  - Created product: Energy Bar (SNK-002)
  - Created product: Whey Protein (SUP-001)
  - Created product: BCAA Supplement (SUP-002)
  - Created product: Gym T-Shirt (MER-001)
  - Created product: Gym Towel (MER-002)
  - Created product: Creatine Monohydrate (SUP-003)
  - Created product: Gym Water Bottle (MER-003)
✅ Created 4 categories, 4 suppliers, and 10 products

💰 Creating purchases and sales...
  - Created purchase: PUR-12345 (156,000៛)
  - Created purchase: PUR-67890 (248,000៛)
  - Created sale: SALE-11111 (15,000៛)
  - Created sale: SALE-22222 (42,000៛)
  - Created sale: SALE-33333 (8,000៛)
✅ Created 2 purchases and 3 sales

🚶 Creating pay-per-visit transactions...
  - Created pay-per-visit: 3 people (12,000៛)
  - Created pay-per-visit: 1 people (4,000៛)
  - Created pay-per-visit: 5 people (20,000៛)
✅ Created 3 pay-per-visit transactions

💳 Creating member payments...
  - Created payment: Dara Sok (120,000៛)
  - Created payment: Bopha Meas (300,000៛)
  - Created payment: Srey Prak (120,000៛)
✅ Created 3 member payments

💼 Creating salary payments...
  - Created salary payment: Sokha Meas (900,000៛)
  - Created salary payment: Bopha Chhay (800,000៛)
  - Created salary payment: Sreypov Prak (800,000៛)
  - Created salary payment: Srey Tep (600,000៛)
✅ Created 4 salary payments

🏦 Creating finance transactions...
  - Created deposit: DEP-54321 (2,000,000៛)
  - Created deposit: DEP-98765 (1,500,000៛)
  - Created withdrawal: WDR-13579 (500,000៛)
✅ Created 3 finance transactions

📄 Creating bills...
  - Created bill: electricity - EDC Cambodia (1,200,000៛)
  - Created bill: water - PPWSA (300,000៛)
  - Created bill: rent - Building Owner (6,000,000៛)
✅ Created 3 bills

📊 Demo Data Summary:
==================================================
  Users/Employees     :   6
  Members             :   5
  Packages            :   4
  Products            :  10
  Suppliers           :   4
  Categories          :   4
  Purchases           :   2
  Sales               :   3
  Pay-per-visit       :   3
  Member Payments     :   3
  Salary Payments     :   4
  Finance Transactions:   3
  Bills               :   3

💰 Current Gym Funds: 3,000,000៛

🎯 Demo data creation completed successfully!
   All amounts follow Cambodian currency conventions (KHR in increments of 100)
   Login credentials for demo users: password123

✅ Data reset and demo creation completed successfully!
```

## Technical Details

### Dependencies
- Django 5.1.7
- All project apps (core, members, payments, product, etc.)
- Python standard libraries (random, datetime, etc.)

### Database Impact
- Uses `transaction.atomic()` for data integrity
- Cleans legacy tables via raw SQL
- Respects foreign key relationships
- Safe to run multiple times

### Performance
- Optimized for single-run execution
- Batch operations where possible
- Completes in under 60 seconds
- Minimal database queries

## Best Practices

1. **Always backup** your database before running this command
2. **Test first** by running without `--confirm` to see what will happen
3. **Use in development** environments primarily
4. **Verify preserved users** exist before running
5. **Check output** for any warnings or errors

## Troubleshooting

### Common Issues
- **No admin users found**: Ensure preserved users exist and have admin role
- **Missing dependencies**: Ensure all apps are properly installed
- **Permission errors**: Run with appropriate database permissions

### Error Recovery
- Command uses transactions - partial failures are rolled back
- Re-run the command to retry after fixing issues
- Check Django logs for detailed error information

## Integration

This command integrates with the existing gym management system:
- **Settings**: Uses existing settings and metadata models
- **Permissions**: Respects existing permission system
- **Currency**: Follows established currency formatting
- **Templates**: Compatible with existing print templates

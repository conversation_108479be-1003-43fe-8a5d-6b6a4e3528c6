from django import template
from decimal import Decimal
from core.templatetags.currency_formatters import format_khr as format_khr_with_separators
from core.templatetags.currency_formatters import format_usd as format_usd_with_separators
from core.templatetags.currency_formatters import format_number
from core.templatetags.currency_formatters import format_dual_currency as format_dual_currency_func
from core.templatetags.currency_formatters import format_khr_with_usd_small as format_khr_with_usd_small_func
from core.templatetags.currency_formatters import convert_khr_to_usd as convert_khr_to_usd_func

register = template.Library()

@register.filter
def format_khr(value):
    """
    Format a number as KHR currency with thousand separators
    """
    return format_khr_with_separators(value)

@register.filter
def format_usd(value):
    """
    Format a number as USD currency with thousand separators
    """
    return format_usd_with_separators(value)

@register.filter
def format_dual_currency(value):
    """
    Format a KHR value with USD conversion for dual currency display
    """
    return format_dual_currency_func(value)

@register.filter
def format_khr_with_usd_small(value):
    """
    Format a KHR value with small USD conversion text for reports
    """
    return format_khr_with_usd_small_func(value)

@register.filter
def convert_khr_to_usd(value):
    """
    Convert a KHR value to USD using the current exchange rate
    """
    return convert_khr_to_usd_func(value)

@register.filter
def format_number_with_commas(value):
    """
    Format a number with thousand separators without any currency symbol
    """
    return format_number(value)

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument
    """
    try:
        return int(float(value)) * int(float(arg))
    except (ValueError, TypeError):
        return 0

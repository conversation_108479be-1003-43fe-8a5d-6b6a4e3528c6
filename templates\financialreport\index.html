{% extends "base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <!-- Header with title and period selection -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-blue-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                    <p class="text-gray-600 mt-1"> overview and analysis</p>
                </div>
            </div>

            <!-- Date Range Selector -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div class="flex flex-wrap items-center mb-3">
                    <span class="text-sm font-medium text-gray-700 mr-3">Period:</span>
                    <div class="flex flex-wrap gap-2">
                        <a href="?filter=today" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'today' %}bg-blue-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Today</a>
                        <a href="?filter=week" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'week' %}bg-blue-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Week</a>
                        <a href="?filter=month" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'month' %}bg-blue-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Month</a>
                        <a href="?filter=year" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'year' %}bg-blue-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Year</a>
                        <button id="custom-range-btn" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'custom' %}bg-blue-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Custom Range</button>
                    </div>
                </div>

                <!-- Custom Date Range Form -->
                <div id="custom-range-form" class="{% if filter != 'custom' %}hidden{% endif %} mt-3 pt-3 border-t border-gray-200">
                    <form method="get" class="flex flex-wrap gap-4 items-end">
                        <input type="hidden" name="filter" value="custom">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" name="start_date" value="{{ start_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" name="end_date" value="{{ end_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">Apply</button>
                    </form>
                </div>

                <div class="mt-3 text-sm text-gray-600 flex items-center">
                    <i class="fa-regular fa-calendar mr-2"></i>
                    <span>Showing data for: <strong>{{ date_range }}</strong></span>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-blue-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Income</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ income_total|format_khr }}</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-trend-up text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-red-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Expenses</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ expense_total|format_khr }}</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-trend-down text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 {% if balance >= 0 %}border-green-500{% else %}border-yellow-500{% endif %} transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Net Balance</p>
                        <p class="text-3xl font-bold {% if balance >= 0 %}text-green-600{% else %}text-yellow-600{% endif %} mt-1">{{ balance|format_khr }}</p>
                    </div>
                    <div class="{% if balance >= 0 %}bg-green-100{% else %}bg-yellow-100{% endif %} p-3 rounded-full">
                        <i class="fa-solid fa-scale-balanced {% if balance >= 0 %}text-green-600{% else %}text-yellow-600{% endif %} text-xl"></i>
                    </div>
                </div>
            </div>
        </div>



        <!-- Charts Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Income Sources Chart -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Income Sources</h3>
                <div class="h-64">
                    <canvas id="incomeSourcesChart"></canvas>
                </div>
                <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Membership</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ income_sources.membership.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ income_sources.membership.percentage }}%</p>
                    </div>
                    <div class="bg-teal-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Product Sales</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ income_sources.product_sales.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ income_sources.product_sales.percentage }}%</p>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Pay-per-visit</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ income_sources.paypervisit.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ income_sources.paypervisit.percentage }}%</p>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Deposits</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ income_sources.deposits.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ income_sources.deposits.percentage }}%</p>
                    </div>
                </div>
            </div>

            <!-- Expense Categories Chart -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Expense Categories</h3>
                <div class="h-64">
                    <canvas id="expenseCategoriesChart"></canvas>
                </div>
                <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-red-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Salaries</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ expense_categories.salaries.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ expense_categories.salaries.percentage }}%</p>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Bills</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ expense_categories.bills.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ expense_categories.bills.percentage }}%</p>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Purchases</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ expense_categories.purchases.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ expense_categories.purchases.percentage }}%</p>
                    </div>
                    <div class="bg-blue-50 p-3 rounded-lg text-center">
                        <p class="text-sm font-medium text-gray-600">Withdrawals</p>
                        <p class="text-lg font-bold text-gray-800 mt-1">{{ expense_categories.withdrawals.total|format_khr }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ expense_categories.withdrawals.percentage }}%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Links -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <a href="{% url 'financialreport:income_report' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:border-blue-500 transition-all hover:shadow-md group">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">Income Report</h3>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fa-solid fa-money-bill-trend-up text-blue-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Detailed breakdown of all income sources and transactions.</p>
                <div class="text-blue-600 font-medium flex items-center">
                    View Report <i class="fa-solid fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <a href="{% url 'financialreport:expense_report' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:border-blue-500 transition-all hover:shadow-md group">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">Expense Report</h3>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fa-solid fa-money-bill-transfer text-red-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Complete analysis of all expenses by category and date.</p>
                <div class="text-blue-600 font-medium flex items-center">
                    View Report <i class="fa-solid fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <a href="{% url 'financialreport:balance_report' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:border-blue-500 transition-all hover:shadow-md group">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">Balance Report</h3>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fa-solid fa-scale-balanced text-green-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Comprehensive view of income vs expenses and net balance.</p>
                <div class="text-blue-600 font-medium flex items-center">
                    View Report <i class="fa-solid fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle custom date range form
        const customRangeBtn = document.getElementById('custom-range-btn');
        const customRangeForm = document.getElementById('custom-range-form');

        if (customRangeBtn) {
            customRangeBtn.addEventListener('click', function() {
                customRangeForm.classList.toggle('hidden');
            });
        }

        // Chart.js Global Configuration
        Chart.defaults.font.family = "'Inter', 'Helvetica', 'Arial', sans-serif";
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#64748b';

        // Income Sources Chart
        const incomeSourcesCtx = document.getElementById('incomeSourcesChart').getContext('2d');
        new Chart(incomeSourcesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Membership', 'Product Sales', 'Pay-per-visit', 'Deposits'],
                datasets: [{
                    data: [
                        {{ income_sources.membership.total }},
                        {{ income_sources.product_sales.total }},
                        {{ income_sources.paypervisit.total }},
                        {{ income_sources.deposits.total }}
                    ],
                    backgroundColor: [
                        'rgba(37, 99, 235, 0.8)',  // Blue
                        'rgba(20, 184, 166, 0.8)', // Teal
                        'rgba(124, 58, 237, 0.8)', // Purple
                        'rgba(34, 197, 94, 0.8)'   // Green
                    ],
                    borderColor: [
                        'rgba(37, 99, 235, 1)',
                        'rgba(20, 184, 166, 1)',
                        'rgba(124, 58, 237, 1)',
                        'rgba(34, 197, 94, 1)'
                    ],
                    borderWidth: 1,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(17, 24, 39, 0.9)',
                        padding: 12,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                let percentage = context.parsed;
                                return `${value.toLocaleString()} KHR (${percentage.toFixed(1)}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Expense Categories Chart
        const expenseCategoriesCtx = document.getElementById('expenseCategoriesChart').getContext('2d');
        new Chart(expenseCategoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Salaries', 'Bills', 'Purchases', 'Withdrawals'],
                datasets: [{
                    data: [
                        {{ expense_categories.salaries.total }},
                        {{ expense_categories.bills.total }},
                        {{ expense_categories.purchases.total }},
                        {{ expense_categories.withdrawals.total }}
                    ],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',   // Red
                        'rgba(249, 115, 22, 0.8)',  // Orange
                        'rgba(234, 179, 8, 0.8)',   // Yellow
                        'rgba(37, 99, 235, 0.8)'    // Blue
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(249, 115, 22, 1)',
                        'rgba(234, 179, 8, 1)',
                        'rgba(37, 99, 235, 1)'
                    ],
                    borderWidth: 1,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(17, 24, 39, 0.9)',
                        padding: 12,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                let percentage = context.parsed;
                                return `${value.toLocaleString()} KHR (${percentage.toFixed(1)}%)`;
                            }
                        }
                    }
                }
            }
        });


    });
</script>
{% endblock %}

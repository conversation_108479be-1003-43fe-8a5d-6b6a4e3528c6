#!/usr/bin/env python
"""
Test script to verify WebSocket API endpoints are working
"""

import os
import sys
import django
import requests
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

User = get_user_model()

def test_permission_api():
    """Test the permission API endpoint"""
    print("🧪 Testing Permission API Endpoint")
    print("=" * 50)
    
    try:
        # Get a test user
        user = User.objects.filter(is_active=True).first()
        if not user:
            print("❌ No active users found")
            return False
        
        print(f"📋 Testing with user: {user.username} (role: {user.role})")
        
        # Create a test client and login
        client = Client()
        client.force_login(user)
        
        # Test the permission check endpoint
        response = client.get('/settings/api/permissions/check/')
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response: {data}")
            
            if data.get('success'):
                permissions = data.get('permissions', {})
                print(f"📋 Permissions found: {len(permissions)} modules")
                
                for module, level in permissions.items():
                    print(f"   - {module}: {level}")
                
                return True
            else:
                print(f"❌ API returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_status():
    """Test WebSocket status endpoint"""
    print("\n🔌 Testing WebSocket Status Endpoint")
    print("=" * 50)
    
    try:
        # Get a test user
        user = User.objects.filter(is_active=True).first()
        if not user:
            print("❌ No active users found")
            return False
        
        # Create a test client and login
        client = Client()
        client.force_login(user)
        
        # Test the WebSocket status endpoint
        response = client.get('/settings/api/websocket/status/')
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ WebSocket Status: {data}")
            return data.get('websocket_available', False)
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_permission_health():
    """Test permission health check endpoint"""
    print("\n🏥 Testing Permission Health Check")
    print("=" * 50)
    
    try:
        # Get a test user
        user = User.objects.filter(is_active=True).first()
        if not user:
            print("❌ No active users found")
            return False
        
        # Create a test client and login
        client = Client()
        client.force_login(user)
        
        # Test the health check endpoint
        response = client.get('/settings/api/permissions/health/')
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: {data}")
            return data.get('success', False)
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 WebSocket API Test Suite")
    print("=" * 60)
    
    # Test 1: Permission API
    api_test = test_permission_api()
    
    # Test 2: WebSocket Status
    ws_test = test_websocket_status()
    
    # Test 3: Health Check
    health_test = test_permission_health()
    
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"Permission API: {'✅ PASS' if api_test else '❌ FAIL'}")
    print(f"WebSocket Status: {'✅ PASS' if ws_test else '❌ FAIL'}")
    print(f"Health Check: {'✅ PASS' if health_test else '❌ FAIL'}")
    
    if all([api_test, ws_test, health_test]):
        print("\n🎉 All tests passed! WebSocket API is working correctly.")
        print("💡 You can now test the WebSocket page at: http://127.0.0.1:8000/test-websocket/")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
    
    return all([api_test, ws_test, health_test])

if __name__ == "__main__":
    main()

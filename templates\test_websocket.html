<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Legend Fitness Club</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-section {
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WebSocket Real-Time Permission Test</h1>
        <p><strong>User:</strong> {{ user.username }} ({{ user.role }})</p>

        <div class="test-section">
            <h3>📡 WebSocket Connection Status</h3>
            <div id="permissionStatus" class="status disconnected">Permission WebSocket: Disconnected</div>
            <div id="notificationStatus" class="status disconnected">Notification WebSocket: Disconnected</div>
        </div>

        <div class="test-section">
            <h3>🔧 Test Controls</h3>
            <button onclick="refreshPermissions()">Refresh Permissions</button>
            <button onclick="testPermissionUpdate()">Test Permission Update</button>
            <button onclick="testNotification()">Test Notification</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📝 WebSocket Message Log</h3>
            <div id="messageLog" class="log">Waiting for WebSocket connections...</div>
        </div>

        <div class="test-section">
            <h3>📋 Current Permissions</h3>
            <div id="currentPermissions" class="log">Loading permissions...</div>
        </div>
    </div>

    <script>
        let permissionSocket = null;
        let notificationSocket = null;
        let messageCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('messageLog');
            const colorClass = type === 'error' ? 'color: red;' :
                              type === 'success' ? 'color: green;' :
                              type === 'warning' ? 'color: orange;' : 'color: black;';

            logDiv.innerHTML += `<div style="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            messageCount++;
        }

        function updateStatus(elementId, connected, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${connected ? 'connected' : 'disconnected'}`;
            element.textContent = message;
        }

        function connectPermissionWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/permissions/`;

            log(`Connecting to Permission WebSocket: ${wsUrl}`);

            permissionSocket = new WebSocket(wsUrl);

            permissionSocket.onopen = function(e) {
                log('✅ Permission WebSocket connected successfully!', 'success');
                updateStatus('permissionStatus', true, 'Permission WebSocket: Connected');
            };

            permissionSocket.onmessage = function(e) {
                const data = JSON.parse(e.data);
                log(`📨 Permission message received: ${JSON.stringify(data, null, 2)}`, 'success');

                // Handle both initial sync and updates
                if (data.type === 'permission_sync' || data.type === 'permission_update') {
                    log(`🔄 Permission ${data.type === 'permission_sync' ? 'sync' : 'update'} for role: ${data.user_role}`, 'warning');
                    log(`📋 Permissions: ${JSON.stringify(data.permissions, null, 2)}`, 'info');
                    updateCurrentPermissions(data.permissions);
                }
            };

            permissionSocket.onclose = function(e) {
                log('❌ Permission WebSocket disconnected', 'error');
                updateStatus('permissionStatus', false, 'Permission WebSocket: Disconnected');

                // Load permissions via API as fallback
                loadPermissionsViaAPI();

                // Attempt to reconnect after 3 seconds
                setTimeout(connectPermissionWebSocket, 3000);
            };

            permissionSocket.onerror = function(e) {
                log('🚨 Permission WebSocket error occurred', 'error');
                // Load permissions via API as fallback
                loadPermissionsViaAPI();
            };
        }

        function connectNotificationWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;

            log(`Connecting to Notification WebSocket: ${wsUrl}`);

            notificationSocket = new WebSocket(wsUrl);

            notificationSocket.onopen = function(e) {
                log('✅ Notification WebSocket connected successfully!', 'success');
                updateStatus('notificationStatus', true, 'Notification WebSocket: Connected');
            };

            notificationSocket.onmessage = function(e) {
                const data = JSON.parse(e.data);
                log(`🔔 Notification message received: ${JSON.stringify(data, null, 2)}`, 'success');
            };

            notificationSocket.onclose = function(e) {
                log('❌ Notification WebSocket disconnected', 'error');
                updateStatus('notificationStatus', false, 'Notification WebSocket: Disconnected');

                // Attempt to reconnect after 3 seconds
                setTimeout(connectNotificationWebSocket, 3000);
            };

            notificationSocket.onerror = function(e) {
                log('🚨 Notification WebSocket error occurred', 'error');
            };
        }

        function updateCurrentPermissions(permissions) {
            const permDiv = document.getElementById('currentPermissions');
            if (permissions && Object.keys(permissions).length > 0) {
                permDiv.innerHTML = `<pre>${JSON.stringify(permissions, null, 2)}</pre>`;
                log(`✅ Permissions loaded successfully (${Object.keys(permissions).length} modules)`, 'success');
            } else {
                permDiv.innerHTML = '<div style="color: orange;">No permissions found or empty permission set</div>';
                log('⚠️ No permissions found', 'warning');
            }
        }

        function loadPermissionsViaAPI() {
            log('🔄 Loading permissions via API fallback...', 'warning');

            fetch('/settings/api/permissions/check/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.permissions) {
                    log(`📋 Permissions loaded via API for role: ${data.user_role}`, 'success');
                    updateCurrentPermissions(data.permissions);
                } else {
                    log(`❌ API response error: ${data.error || 'Unknown error'}`, 'error');
                    document.getElementById('currentPermissions').innerHTML =
                        `<div style="color: red;">Failed to load permissions: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                log(`❌ Error loading permissions via API: ${error.message}`, 'error');
                document.getElementById('currentPermissions').innerHTML =
                    `<div style="color: red;">API Error: ${error.message}</div>`;
            });
        }

        function refreshPermissions() {
            log('🔄 Manually refreshing permissions...', 'info');

            if (permissionSocket && permissionSocket.readyState === WebSocket.OPEN) {
                // Request permission check via WebSocket
                permissionSocket.send(JSON.stringify({
                    type: 'permission_check'
                }));
                log('📤 Permission check request sent via WebSocket', 'info');
            } else {
                // Fallback to API
                loadPermissionsViaAPI();
            }
        }

        function testPermissionUpdate() {
            log('🧪 Triggering permission update test...', 'warning');

            // Make AJAX call to trigger permission update
            fetch('/test-permission-update/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    'test': true
                })
            })
            .then(response => response.json())
            .then(data => {
                log(`📤 Permission update triggered: ${JSON.stringify(data)}`, 'info');
            })
            .catch(error => {
                log(`❌ Error triggering permission update: ${error}`, 'error');
            });
        }

        function testNotification() {
            log('🧪 Triggering notification test...', 'warning');

            // Make AJAX call to trigger notification
            fetch('/test-notification/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    'message': 'Test notification from WebSocket test page'
                })
            })
            .then(response => response.json())
            .then(data => {
                log(`📤 Notification triggered: ${JSON.stringify(data)}`, 'info');
            })
            .catch(error => {
                log(`❌ Error triggering notification: ${error}`, 'error');
            });
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
            messageCount = 0;
            log('🧹 Log cleared');
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Initialize WebSocket connections when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Initializing WebSocket test page...');

            // Load permissions immediately via API (fallback)
            loadPermissionsViaAPI();

            // Then connect WebSockets for real-time updates
            connectPermissionWebSocket();
            connectNotificationWebSocket();
        });
    </script>
</body>
</html>

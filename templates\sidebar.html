{% load static %}
{% load i18n %}
{% load permission_tags %}

<!-- Dashboard section starts  -->
<div
  class="dashboardContainer fixed top-0 left-0 z-40 w-64 h-screen transition-all duration-300 ease-in-out -translate-x-full sm:translate-x-0 shadow-lg"
  aria-label="Sidebar"
>
  <div class="h-full px-0 py-0 bg-blue-900 flex flex-col">
    <!-- Dashboard top section  -->
    <div
      class="dashBoardTop mb-6 flex flex-col justify-center items-center text-white pt-6 pb-4"
    >
      <!-- Logo -->
      <div class="logo-section mb-3">
        <div class="w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-md">
          <span class="text-blue-600 font-bold text-base">LF</span>
        </div>
      </div>

      <!-- User info -->
      <div class="user-info text-center px-2">
        <h1 class="text-sm font-semibold text-white mb-1 truncate max-w-full">{{request.user.username}}</h1>
        <p class="text-xs text-blue-100 font-medium">{{request.user.role|title}}</p>
      </div>
    </div>
    <!-- Dashboard top section ends  -->

    <!-- Dashboard lists  -->
    <div class="dashBoardLinks flex-grow overflow-y-auto px-2">
      <ul class="space-y-1 pb-4">
        {% if request.user.role == 'admin' %}
        <!-- Dashboard -->
        <li>
          <a
            href="{% url "adminDashboard" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-tachometer-alt flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Dashboard" %}</span>
          </a>
        </li>

        <!-- Pay-per-visit -->
        {% has_permission request.user 'paypervisit' 'view' as can_view_paypervisit %}
        {% if can_view_paypervisit %}
        <li>
          <a
            href="{% url "paypervisit:index" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-walking flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Pay-per-visit" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- POS -->
        {% has_permission request.user 'pos' 'view' as can_view_pos %}
        {% if can_view_pos %}
        <li>
          <a
            href="{% url "product:pos" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-cash-register flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "POS" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- Staff and User Section -->
        {% has_permission request.user 'user' 'view' as can_view_users %}
        {% if can_view_users %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#staffSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-users flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Staff and User" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="staffSubmenu" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              <li>
                <a
                  href="{% url "user:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'user' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-user-tie flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Employee" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url "user:register" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'user' and request.resolver_match.url_name == 'register' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-user-plus flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "User" %}</span>
                </a>
              </li>
            </ul>
          </div>
        </li>
        {% endif %}

        <!-- Membership Section -->
        {% has_permission request.user 'member' 'view' as can_view_members %}
        {% if can_view_members %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white {% if request.resolver_match.namespace == 'member' %}bg-blue-500 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#membershipSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-id-card flex-shrink-0 w-4 text-center mr-3 {% if request.resolver_match.namespace == 'member' %}text-blue-200{% else %}text-blue-100{% endif %}"></i>
              <span class="font-medium text-sm truncate">{% trans "Membership" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="membershipSubmenu" style="{% if request.resolver_match.namespace == 'member' %}display: block;{% else %}display: none;{% endif %}">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              <li>
                <a
                  href="{% url "member:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'member' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-user-friends flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Members" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url 'member:package_list' %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'member' and request.resolver_match.url_name == 'package_list' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-box flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Packages" %}</span>
                </a>
              </li>
            </ul>
          </div>
        </li>
        {% endif %}

        <!-- Products Section -->
        {% has_permission request.user 'product' 'view' as can_view_products %}
        {% has_permission request.user 'purchase' 'view' as can_view_purchases %}
        {% has_permission request.user 'pos' 'view' as can_view_pos %}
        {% if can_view_products or can_view_purchases or can_view_pos %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#productSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-shopping-basket flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Products" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="productSubmenu" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              {% if can_view_products %}
              <li>
                <a
                  href="{% url "product:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-list flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Products" %}</span>
                </a>
              </li>
              {% endif %}
              {% if can_view_pos %}
              <li>
                <a
                  href="{% url "product:pos" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'pos' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-cash-register flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "POS" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_purchases %}
              <li>
                <a
                  href="{% url "product:purchases" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'purchases' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-truck-loading flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Purchase Products" %}</span>
                </a>
              </li>

              <li>
                <a
                  href="{% url "product:suppliers" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'suppliers' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-truck flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Suppliers" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_products %}
              <li>
                <a
                  href="{% url "product:categories" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'categories' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-tags flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Category" %}</span>
                </a>
              </li>
              {% endif %}
            </ul>
          </div>
        </li>
        {% endif %}

        <!-- Payments Section -->
        {% has_permission request.user 'payment' 'view' as can_view_payments %}
        {% has_permission request.user 'paypervisit' 'view' as can_view_paypervisit %}
        {% has_permission request.user 'payroll' 'view' as can_view_payroll %}
        {% has_permission request.user 'bill' 'view' as can_view_bills %}
        {% if can_view_payments or can_view_paypervisit or can_view_payroll or can_view_bills %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#paymentsSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-money-bill-wave flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Payments" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="paymentsSubmenu" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              {% if can_view_payments %}
              <li>
                <a
                  href="{% url "payment:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'payment' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-user-tag flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Member Payments" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_paypervisit %}
              <li>
                <a
                  href="{% url "paypervisit:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'paypervisit' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-walking flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Pay-per-visit" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_payroll %}
              <li>
                <a
                  href="{% url "payroll:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'payroll' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-hand-holding-usd flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Payroll" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_bills %}
              <li>
                <a
                  href="{% url "billmanagement:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'billmanagement' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-file-invoice-dollar flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Bill Management" %}</span>
                </a>
              </li>
              {% endif %}
            </ul>
          </div>
        </li>
        {% endif %}

        <!-- Finance Section -->
        {% has_permission request.user 'finance' 'view' as can_view_finance %}
        {% if can_view_finance %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#financeSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-wallet flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Finance" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="financeSubmenu" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              <li>
                <a
                  href="{% url "finance:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'finance' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-chart-line flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Overview" %}</span>
                </a>
              </li>
              {% has_permission request.user 'finance' 'edit' as can_edit_finance %}
              {% if can_edit_finance %}
              <li>
                <a
                  href="{% url "finance:deposit" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'finance' and request.resolver_match.url_name == 'deposit' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-arrow-circle-down flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Deposit" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url "finance:withdraw" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'finance' and request.resolver_match.url_name == 'withdraw' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-arrow-circle-up flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Withdraw" %}</span>
                </a>
              </li>
              {% endif %}
              <li>
                <a
                  href="{% url "finance:history" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'finance' and request.resolver_match.url_name == 'history' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-history flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Transaction History" %}</span>
                </a>
              </li>
            </ul>
          </div>
        </li>
        {% endif %}
            <!-- Financial Reports Section -->
        {% has_permission request.user 'financialreport' 'view' as can_view_finance_reports %}
        {% if can_view_finance_reports %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#reportsSubmenu"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-chart-line flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Financial Reports" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="reportsSubmenu" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              <li>
                <a
                  href="{% url "financialreport:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'financialreport' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-chart-bar flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Overview Report" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url "financialreport:income_report" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'financialreport' and request.resolver_match.url_name == 'income_report' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-chart-bar flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Income Report" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url "financialreport:expense_report" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'financialreport' and request.resolver_match.url_name == 'expense_report' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-chart-pie flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Expenses Report" %}</span>
                </a>
              </li>
              <li>
                <a
                  href="{% url "financialreport:balance_report" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'financialreport' and request.resolver_match.url_name == 'balance_report' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-balance-scale flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Balance Report" %}</span>
                </a>
              </li>
            </ul>
          </div>
        </li>
        {% endif %}



        {% else %}
        <!-- Employee Dashboard -->
        <li>
          <a
            href="{% url "employeeDashboard" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white {% if request.resolver_match.url_name == 'employeeDashboard' %}bg-blue-500 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-tachometer-alt flex-shrink-0 w-4 text-center mr-3 {% if request.resolver_match.url_name == 'employeeDashboard' %}text-blue-200{% else %}text-blue-100{% endif %}"></i>
            <span class="font-medium text-sm truncate">{% trans "Dashboard" %}</span>
          </a>
        </li>

        <!-- Pay-per-visit Section -->
        {% has_permission request.user 'paypervisit' 'view' as can_view_paypervisit %}
        {% if can_view_paypervisit %}
        <li>
          <a
            href="{% url "paypervisit:index" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-walking flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Pay-per-visit" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- POS -->
        {% has_permission request.user 'pos' 'view' as can_view_pos %}
        {% if can_view_pos %}
        <li>
          <a
            href="{% url "product:pos" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-cash-register flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "POS" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- Membership - Only show if user has permission -->
        {% has_permission request.user 'member' 'view' as can_view_members %}
        {% if can_view_members %}
        <li>
          <a
            href="{% url "member:index" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-user-friends flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Members" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- Products Section for Employees -->
        {% has_permission request.user 'product' 'view' as can_view_products %}
        {% has_permission request.user 'purchase' 'view' as can_view_purchases %}
        {% has_permission request.user 'paypervisit' 'view' as can_view_paypervisit_trans %}
        {% if can_view_products or can_view_purchases or can_view_paypervisit_trans %}
        <li>
          <a
            class="submenu-toggle flex justify-between items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg cursor-pointer transition-colors duration-200"
            href="#"
            data-target="#productSubmenuEmp"
          >
            <div class="flex items-center min-w-0">
              <i class="fas fa-shopping-basket flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
              <span class="font-medium text-sm truncate">{% trans "Products" %}</span>
            </div>
            <i class="fas fa-chevron-down text-xs text-blue-200 flex-shrink-0 ml-2 transition-transform duration-200"></i>
          </a>
          <div class="submenu overflow-hidden transition-all duration-300 ease-in-out" id="productSubmenuEmp" style="display: none;">
            <ul class="bg-blue-900 rounded-lg mx-1 mt-1">
              {% if can_view_products %}
              <li>
                <a
                  href="{% url "product:index" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'index' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-box flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Products" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_purchases %}
              <li>
                <a
                  href="{% url "product:purchases" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white {% if request.resolver_match.namespace == 'product' and request.resolver_match.url_name == 'purchases' %}bg-green-600 border-l-4 border-blue-300{% else %}hover:bg-blue-800{% endif %} rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-shopping-cart flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Purchases" %}</span>
                </a>
              </li>
              {% endif %}

              {% if can_view_paypervisit_trans %}
              <li>
                <a
                  href="{% url "paypervisit:transaction" %}"
                  class="submenu-link flex items-center px-3 py-2 pl-8 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-history flex-shrink-0 w-3 text-center mr-2 text-blue-200"></i>
                  <span class="text-xs font-medium truncate">{% trans "Pay-per-visit Transactions" %}</span>
                </a>
              </li>
              {% endif %}
            </ul>
          </div>
        </li>
        {% endif %}

        <!-- Payments -->
        {% has_permission request.user 'payment' 'view' as can_view_payments %}
        {% if can_view_payments %}
        <li>
          <a
            href="{% url "payment:index" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-money-bill-wave flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Payments" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- My Salary -->
        {% has_permission request.user 'payroll' 'view' as can_view_payroll %}
        {% if can_view_payroll %}
        <li>
          <a
            href="{% url "payroll:employee" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-wallet flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "My Salary" %}</span>
          </a>
        </li>
        {% endif %}

        <!-- Finance Management -->
        {% has_permission request.user 'finance' 'view' as can_view_finance %}
        {% if can_view_finance %}
        <li>
          <a
            href="{% url "finance:index" %}"
            class="sidebar-link flex items-center px-3 py-2.5 mx-1 text-white hover:bg-blue-800 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-chart-line flex-shrink-0 w-4 text-center mr-3 text-blue-100"></i>
            <span class="font-medium text-sm truncate">{% trans "Finance" %}</span>
          </a>
        </li>
        {% endif %}
      {% endif %}
      </ul>
    </div>

    <!-- Dashboard lists ends   -->

    <!-- Logout Section -->
    <div class="mt-auto px-2 pb-4">
      <!-- Logout Button -->
      <a href="{% url "logout" %}" class="flex items-center justify-between px-3 py-2.5 mx-1 text-white hover:bg-red-600 rounded-lg transition-colors duration-200 border border-red-500/30">
        <div class="flex items-center min-w-0">
          <i class="fas fa-sign-out-alt flex-shrink-0 w-4 text-center mr-3 text-red-200"></i>
          <span class="font-medium text-sm truncate">{% trans "Logout" %}</span>
        </div>
        <i class="fas fa-arrow-right text-xs text-red-200 flex-shrink-0 ml-2"></i>
      </a>
    </div>
    <!-- Logout Section ends -->
  </div>
</div>
<!-- dashboard section ends  -->

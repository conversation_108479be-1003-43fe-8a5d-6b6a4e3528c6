{% extends "../base.html" %}
{% load custom_filters %}
{% load i18n %}

{% load static %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- Page header with breadcrumbs -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-800">User Management</h1>
      <div class="flex items-center text-sm mt-2">
        <a href="{% url 'adminDashboard' %}" class="text-gray-500 hover:text-blue-600 transition-colors">
          <i class="fas fa-home mr-1"></i>Dashboard
        </a>
        <span class="mx-2 text-gray-400">/</span>
        <span class="text-blue-600 font-medium">User Management</span>
      </div>
    </div>



    <!-- Tabbed interface -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- Tab navigation -->
      <div class="border-b border-gray-200">
        <nav class="flex">
          <button class="px-6 py-3 text-sm font-medium border-b-2 border-blue-500 text-blue-600 whitespace-nowrap flex items-center active" data-tab="employees"><i class="fa-solid fa-users mr-2"></i> Employees</button>
          <button class="px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap flex items-center" data-tab="system-users"><i class="fa-solid fa-key mr-2"></i> System Access</button>
          <button class="px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap flex items-center" data-tab="admins"><i class="fa-solid fa-user-shield mr-2"></i> Admin Users</button>
        </nav>
      </div>

      <!-- Employees tab content -->
      <div id="employees-tab" class="block p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-800">Employees List</h3>

          <!-- Action button -->
          <div>
            <a href="{% url 'user:add_employee' %}" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
              <i class="fa-solid fa-user-plus mr-2"></i>Add Employee
            </a>
          </div>
        </div>

        <!-- Search and Filter Controls -->
        <div class="flex flex-wrap gap-4 mb-4">
          <!-- Search Box -->
          <div class="flex-grow max-w-md">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="fa-solid fa-search text-gray-500"></i>
              </div>
              <input type="text" id="employeeSearch" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Search by name, ID, or phone...">
            </div>
          </div>

          <!-- Role Filter -->
          <div class="w-48">
            <select id="roleFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="cashier">Cashier</option>
              <option value="coach">Coach</option>
              <option value="cleaner">Cleaner</option>
              <option value="security">Security Guard</option>
            </select>
          </div>

          <!-- Status Filter -->
          <div class="w-48">
            <select id="statusFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <!-- System Access Filter -->
          <div class="w-48">
            <select id="accessFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">All Access Types</option>
              <option value="yes">Has System Access</option>
              <option value="no">No System Access</option>
            </select>
          </div>
        </div>

        <!-- Employee Cards View Toggle -->
        <div class="flex justify-end mb-4">
          <div class="inline-flex rounded-md shadow-sm" role="group">
            <button type="button" id="tableViewBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-700 border border-blue-700 rounded-l-lg hover:bg-blue-800 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:bg-blue-800"><i class="fa-solid fa-table-list mr-2"></i> Table</button>
            <button type="button" id="cardViewBtn" class="px-4 py-2 text-sm font-medium text-blue-700 bg-white border border-blue-700 rounded-r-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700"><i class="fa-solid fa-id-card mr-2"></i> Cards</button>
          </div>
        </div>

        <!-- Table View -->
        <div id="tableView" class="overflow-x-auto">
          <table class="w-full">
            <thead class="text-sm uppercase bg-blue-700 text-white text-center"><tr>
                <th scope="col" class="px-6 py-3">Photo</th>
                <th scope="col" class="px-6 py-3">Employee ID</th>
                <th scope="col" class="px-6 py-3">Name</th>
                <th scope="col" class="px-6 py-3">Role</th>
                <th scope="col" class="px-6 py-3">Phone</th>
                <th scope="col" class="px-6 py-3">Salary</th>
                <th scope="col" class="px-6 py-3">Join Date</th>
                <th scope="col" class="px-6 py-3">Status</th>
                <th scope="col" class="px-6 py-3">System Access</th>
                <th scope="col" class="px-6 py-3">Actions</th>
              </tr>
            </thead>
            <tbody id="employeeTableBody">
              {% for employee in employees %}
              <tr class="bg-white border-b hover:bg-gray-50 text-sm text-center employee-row"
                  data-name="{{ employee.name|default:employee.username|lower }}"
                  data-id="{{ employee.emp_id|default:''}}"
                  data-phone="{{ employee.phone|default:'' }}"
                  data-role="{{ employee.role|default:'' }}"
                  data-status="{% if employee.is_active %}active{% else %}inactive{% endif %}"
                  data-access="{% if employee.is_staff %}yes{% else %}no{% endif %}">
                <td class="px-6 py-4">
                  {% if employee.photo %}
                  <div class="w-10 h-10 mx-auto">
                    <img src="/media/{{ employee.photo }}" alt="{{ employee.name }}" class="w-full h-full rounded-full object-cover border border-gray-200">
                  </div>
                  {% else %}
                  <div class="w-10 h-10 mx-auto bg-gray-100 rounded-full flex items-center justify-center border border-gray-200">
                    <i class="fa-solid fa-user text-gray-500"></i>
                  </div>
                  {% endif %}
                </td>
                <td class="px-6 py-4">{{ employee.emp_id|default:"" }}</td>
                <td class="px-6 py-4 font-medium">{{ employee.name|default:employee.username }}</td>
                <td class="px-6 py-4">
                  {% if employee.role == 'admin' %}
                  <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Admin</span>
                  {% elif employee.role == 'cashier' %}
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Cashier</span>
                  {% elif employee.role == 'coach' %}
                  <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Coach</span>
                  {% elif employee.role == 'cleaner' %}
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Cleaner</span>
                  {% elif employee.role == 'security' %}
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Security</span>
                  {% else %}
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No Role</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">{{ employee.phone|default:"" }}</td>
                <td class="px-6 py-4">{% if employee.salary %}{{ employee.salary|format_khr }}{% endif %}</td>
                <td class="px-6 py-4">{{ employee.join_date|default:"" }}</td>
                <td class="px-6 py-4">
                  {% if employee.is_active %}
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                  {% else %}
                  <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  {% if employee.is_staff %}
                  <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Yes</span>
                  {% else %}
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  <div class="flex justify-center space-x-2">
                    <a href="{% url 'user:edit' employee.id %}" class="text-blue-600 hover:text-blue-800" title="Edit">
                      <i class="fa-solid fa-pen-to-square"></i>
                    </a>
                    {% if employee.is_active %}
                    <a href="{% url 'user:deactivate' employee.id %}" class="text-yellow-600 hover:text-yellow-800" title="Deactivate">
                      <i class="fa-solid fa-user-slash"></i>
                    </a>
                    {% else %}
                    <a href="{% url 'user:active' employee.id %}" class="text-green-600 hover:text-green-800" title="Activate">
                      <i class="fa-solid fa-user-check"></i>
                    </a>
                    {% endif %}
                    <a href="{% url 'user:delete' employee.id %}" class="text-red-600 hover:text-red-800" onclick="return confirm('Are you sure you want to delete this employee?')" title="Delete">
                      <i class="fa-solid fa-trash"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>

          <!-- No Results Message -->
          <div id="noResultsMessage" class="hidden py-8 text-center text-gray-500">
            <i class="fa-solid fa-search text-4xl mb-2"></i>
            <p>No employees match your search criteria.</p>
          </div>
        </div>

        <!-- Card View (Hidden by default) -->
        <div id="cardView" class="hidden">
          <div id="employeeCardContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for employee in employees %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden employee-card hover:shadow-lg transition-shadow"
                 data-name="{{ employee.name|default:employee.username|lower }}"
                 data-id="{{ employee.emp_id|default:''}}"
                 data-phone="{{ employee.phone|default:'' }}"
                 data-role="{{ employee.role|default:'' }}"
                 data-status="{% if employee.is_active %}active{% else %}inactive{% endif %}"
                 data-access="{% if employee.is_staff %}yes{% else %}no{% endif %}">
              <div class="p-5 flex items-center space-x-4">
                {% if employee.photo %}
                <div class="w-16 h-16 flex-shrink-0">
                  <img src="/media/{{ employee.photo }}" alt="{{ employee.name }}" class="w-full h-full rounded-full object-cover border border-gray-200">
                </div>
                {% else %}
                <div class="w-16 h-16 flex-shrink-0 bg-gray-100 rounded-full flex items-center justify-center border border-gray-200">
                  <i class="fa-solid fa-user text-gray-500 text-2xl"></i>
                </div>
                {% endif %}
                <div class="flex-grow">
                  <h3 class="text-lg font-semibold text-gray-800">{{ employee.name|default:employee.username }}</h3>
                  <p class="text-sm text-gray-600">{{ employee.emp_id|default:"No ID" }}</p>
                  <div class="mt-2 flex flex-wrap gap-1">
                    {% if employee.role == 'admin' %}
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Admin</span>
                    {% elif employee.role == 'cashier' %}
                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Cashier</span>
                    {% elif employee.role == 'coach' %}
                    <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Coach</span>
                    {% elif employee.role == 'cleaner' %}
                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Cleaner</span>
                    {% elif employee.role == 'security' %}
                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Security</span>
                    {% else %}
                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No Role</span>
                    {% endif %}

                    {% if employee.is_active %}
                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                    {% else %}
                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                    {% endif %}

                    {% if employee.is_staff %}
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">System Access</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="border-t border-gray-200 px-5 py-4 text-sm bg-gray-50">
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <p class="text-gray-500 text-xs mb-1">Phone</p>
                    <p class="font-medium">{{ employee.phone|default:"Not provided" }}</p>
                  </div>
                  <div>
                    <p class="text-gray-500 text-xs mb-1">Join Date</p>
                    <p class="font-medium">{{ employee.join_date|default:"Not provided" }}</p>
                  </div>
                  <div>
                    <p class="text-gray-500 text-xs mb-1">Salary</p>
                    <p class="font-medium">{% if employee.salary %}{{ employee.salary|format_khr }}{% else %}Not set{% endif %}</p>
                  </div>
                </div>
              </div>
              <div class="px-5 py-3 flex justify-between border-t border-gray-200">
                <a href="{% url 'user:edit' employee.id %}" class="text-blue-600 hover:text-blue-800">
                  <i class="fa-solid fa-pen-to-square mr-1"></i> Edit
                </a>
                {% if employee.is_active %}
                <a href="{% url 'user:deactivate' employee.id %}" class="text-yellow-600 hover:text-yellow-800">
                  <i class="fa-solid fa-user-slash mr-1"></i> Deactivate
                </a>
                {% else %}
                <a href="{% url 'user:active' employee.id %}" class="text-green-600 hover:text-green-800">
                  <i class="fa-solid fa-user-check mr-1"></i> Activate
                </a>
                {% endif %}
                <a href="{% url 'user:delete' employee.id %}" class="text-red-600 hover:text-red-800" onclick="return confirm('Are you sure you want to delete this employee?')">
                  <i class="fa-solid fa-trash mr-1"></i> Delete
                </a>
              </div>
            </div>
            {% endfor %}
          </div>

          <!-- No Results Message (Card View) -->
          <div id="noResultsMessageCards" class="hidden py-8 text-center text-gray-500">
            <i class="fa-solid fa-search text-4xl mb-2"></i>
            <p>No employees match your search criteria.</p>
          </div>
        </div>
      </div>

      <!-- System Access Users Tab Content -->
      <div id="system-users-tab" class="hidden p-6">
        <div class="flex justify-between items-center mb-4">
          <div>
            <h3 class="text-xl font-semibold text-gray-800">System Access Users</h3>
            <p class="text-gray-600 text-sm">Employees with access to the system.</p>
          </div>
          <div class="flex space-x-3">
            {% if request.user.role == 'admin' %}
            <a href="{% url 'user:user_action_logs' %}" class="bg-green-700 hover:bg-green-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
              <i class="fa-solid fa-clipboard-list mr-2"></i>User Action Logs
            </a>
            {% endif %}
            <a href="{% url 'user:register' %}" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
              <i class="fa-solid fa-user-plus mr-2"></i>Add System Access
            </a>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="text-sm uppercase bg-blue-700 text-white text-center"><tr>
                <th scope="col" class="px-6 py-3">Username</th>
                <th scope="col" class="px-6 py-3">Name</th>
                <th scope="col" class="px-6 py-3">Email</th>
                <th scope="col" class="px-6 py-3">Role</th>
                <th scope="col" class="px-6 py-3">Status</th>
                <th scope="col" class="px-6 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for user in system_users %}
              <tr class="bg-white border-b hover:bg-gray-50 text-sm text-center">
                <td class="px-6 py-4 font-medium">{{ user.username }}</td>
                <td class="px-6 py-4">{{ user.name|default:user.username }}</td>
                <td class="px-6 py-4">{{ user.email|default:"" }}</td>
                <td class="px-6 py-4">
                  {% if user.role == 'admin' %}
                  <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Admin</span>
                  {% elif user.role == 'cashier' %}
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Cashier</span>
                  {% elif user.role == 'coach' %}
                  <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Coach</span>
                  {% else %}
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ user.role|default:"No role" }}</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  {% if user.is_active %}
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                  {% else %}
                  <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  <div class="flex justify-center space-x-2">
                    <a href="{% url 'user:edit_system_user' user.id %}" class="text-blue-600 hover:text-blue-800" title="Edit">
                      <i class="fa-solid fa-pen-to-square"></i>
                    </a>
                    {% if user.is_active %}
                    <a href="{% url 'user:deactivate' user.id %}" class="text-yellow-600 hover:text-yellow-800" title="Deactivate">
                      <i class="fa-solid fa-user-slash"></i>
                    </a>
                    {% else %}
                    <a href="{% url 'user:active' user.id %}" class="text-green-600 hover:text-green-800" title="Activate">
                      <i class="fa-solid fa-user-check"></i>
                    </a>
                    {% endif %}
                    <a href="{% url 'user:delete' user.id %}" class="text-red-600 hover:text-red-800" onclick="return confirm('Are you sure you want to delete this user?')" title="Delete">
                      <i class="fa-solid fa-trash"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% empty %}
              <tr class="bg-white border-b text-sm text-center">
                <td colspan="6" class="px-6 py-4 text-gray-500">No users with system access found.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

      <!-- Admin Users Tab Content -->
      <div id="admins-tab" class="hidden p-6">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-2 sm:space-y-0">
          <h3 class="text-xl font-semibold text-gray-800">Admin Users List</h3>
          <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <a href="{% url 'user:change_password' %}" class="bg-green-700 hover:bg-green-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
              <i class="fas fa-key mr-2"></i>{% trans "Change Password" %}
            </a>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="text-sm uppercase bg-purple-700 text-white text-center"><tr>
                <th scope="col" class="px-6 py-3">Username</th>
                <th scope="col" class="px-6 py-3">Name</th>
                <th scope="col" class="px-6 py-3">Email</th>
                <th scope="col" class="px-6 py-3">Create Date</th>
                <th scope="col" class="px-6 py-3">Status</th>
                <th scope="col" class="px-6 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for user in admin_users %}
              <tr class="bg-white border-b hover:bg-gray-50 text-sm text-center">
                <td class="px-6 py-4 font-medium">{{ user.username }}</td>
                <td class="px-6 py-4">{{ user.name|default:user.username }}</td>
                <td class="px-6 py-4">{{ user.email|default:"" }}</td>
                <td class="px-6 py-4">{{ user.create_day|date:"Y-m-d" }}</td>
                <td class="px-6 py-4">
                  {% if user.is_active %}
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                  {% else %}
                  <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  <div class="flex justify-center space-x-2">
                    <a href="{% url 'user:edit_admin_user' user.id %}" class="text-blue-600 hover:text-blue-800" title="Edit">
                      <i class="fa-solid fa-pen-to-square"></i>
                    </a>
                    {% if user.is_active %}
                    <a href="{% url 'user:deactivate' user.id %}" class="text-yellow-600 hover:text-yellow-800" title="Deactivate">
                      <i class="fa-solid fa-user-slash"></i>
                    </a>
                    {% else %}
                    <a href="{% url 'user:active' user.id %}" class="text-green-600 hover:text-green-800" title="Activate">
                      <i class="fa-solid fa-user-check"></i>
                    </a>
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% empty %}
              <tr class="bg-white border text-sm text-center">
                <td colspan="6" class="px-6 py-4">No admin users found.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>


<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('[data-tab]');
    const employeesTab = document.getElementById('employees-tab');
    const systemUsersTab = document.getElementById('system-users-tab');
    const adminsTab = document.getElementById('admins-tab');

    // Function to switch tabs
    function switchToTab(tabId) {
      // Remove active class from all buttons
      tabButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.classList.remove('border-blue-500');
        btn.classList.remove('text-blue-600');
        btn.classList.add('border-transparent');
        btn.classList.add('text-gray-500');
      });

      // Add active class to the button for this tab
      const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
      if (activeButton) {
        activeButton.classList.add('active');
        activeButton.classList.remove('border-transparent');
        activeButton.classList.remove('text-gray-500');
        activeButton.classList.add('border-blue-500');
        activeButton.classList.add('text-blue-600');
      }

      // Hide all tabs
      employeesTab.classList.add('hidden');
      employeesTab.classList.remove('block');
      systemUsersTab.classList.add('hidden');
      systemUsersTab.classList.remove('block');
      adminsTab.classList.add('hidden');
      adminsTab.classList.remove('block');

      // Show the selected tab
      if (tabId === 'employees') {
        employeesTab.classList.remove('hidden');
        employeesTab.classList.add('block');
      } else if (tabId === 'system-users') {
        systemUsersTab.classList.remove('hidden');
        systemUsersTab.classList.add('block');
      } else if (tabId === 'admins') {
        adminsTab.classList.remove('hidden');
        adminsTab.classList.add('block');
      }
    }

    // Check URL parameters for tab selection
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    if (tabParam === 'admins') {
      switchToTab('admins');
    } else if (tabParam === 'system-users') {
      switchToTab('system-users');
    } else {
      switchToTab('employees');
    }

    // Add click event listeners to tab buttons
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');
        switchToTab(tabId);
      });
    });

    // Get DOM elements for employee filtering
    const employeeSearch = document.getElementById('employeeSearch');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const accessFilter = document.getElementById('accessFilter');
    const tableViewBtn = document.getElementById('tableViewBtn');
    const cardViewBtn = document.getElementById('cardViewBtn');
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const employeeRows = document.querySelectorAll('.employee-row');
    const employeeCards = document.querySelectorAll('.employee-card');
    const noResultsMessage = document.getElementById('noResultsMessage');
    const noResultsMessageCards = document.getElementById('noResultsMessageCards');

    // View toggle functionality
    if (tableViewBtn && cardViewBtn) {
      tableViewBtn.addEventListener('click', function() {
        tableView.classList.remove('hidden');
        cardView.classList.add('hidden');

        // Update button styles
        tableViewBtn.classList.remove('text-blue-700');
        tableViewBtn.classList.remove('bg-white');
        tableViewBtn.classList.add('text-white');
        tableViewBtn.classList.add('bg-blue-700');

        cardViewBtn.classList.remove('text-white');
        cardViewBtn.classList.remove('bg-blue-700');
        cardViewBtn.classList.add('text-blue-700');
        cardViewBtn.classList.add('bg-white');

        filterEmployees(); // Apply current filters to the table view
      });

      cardViewBtn.addEventListener('click', function() {
        tableView.classList.add('hidden');
        cardView.classList.remove('hidden');

        // Update button styles
        cardViewBtn.classList.remove('text-blue-700');
        cardViewBtn.classList.remove('bg-white');
        cardViewBtn.classList.add('text-white');
        cardViewBtn.classList.add('bg-blue-700');

        tableViewBtn.classList.remove('text-white');
        tableViewBtn.classList.remove('bg-blue-700');
        tableViewBtn.classList.add('text-blue-700');
        tableViewBtn.classList.add('bg-white');

        filterEmployees(); // Apply current filters to the card view
      });
    }

    // Search and filter functionality
    if (employeeSearch) {
      employeeSearch.addEventListener('input', filterEmployees);
    }
    if (roleFilter) {
      roleFilter.addEventListener('change', filterEmployees);
    }
    if (statusFilter) {
      statusFilter.addEventListener('change', filterEmployees);
    }
    if (accessFilter) {
      accessFilter.addEventListener('change', filterEmployees);
    }

    function filterEmployees() {
      if (!employeeSearch) return;

      const searchTerm = employeeSearch.value.toLowerCase();
      const roleValue = roleFilter ? roleFilter.value : '';
      const statusValue = statusFilter ? statusFilter.value : '';
      const accessValue = accessFilter ? accessFilter.value : '';

      let tableVisibleCount = 0;
      let cardVisibleCount = 0;

      // Filter table rows
      employeeRows.forEach(row => {
        const name = row.getAttribute('data-name');
        const id = row.getAttribute('data-id');
        const phone = row.getAttribute('data-phone');
        const role = row.getAttribute('data-role');
        const status = row.getAttribute('data-status');
        const access = row.getAttribute('data-access');

        const matchesSearch = name.includes(searchTerm) ||
                             (id && id.toLowerCase().includes(searchTerm)) ||
                             (phone && phone.includes(searchTerm));
        const matchesRole = roleValue === '' || role === roleValue;
        const matchesStatus = statusValue === '' || status === statusValue;
        const matchesAccess = accessValue === '' || access === accessValue;

        const isVisible = matchesSearch && matchesRole && matchesStatus && matchesAccess;

        row.classList.toggle('hidden', !isVisible);

        if (isVisible) {
          tableVisibleCount++;
        }
      });

      // Filter card elements
      employeeCards.forEach(card => {
        const name = card.getAttribute('data-name');
        const id = card.getAttribute('data-id');
        const phone = card.getAttribute('data-phone');
        const role = card.getAttribute('data-role');
        const status = card.getAttribute('data-status');
        const access = card.getAttribute('data-access');

        const matchesSearch = name.includes(searchTerm) ||
                             (id && id.toLowerCase().includes(searchTerm)) ||
                             (phone && phone.includes(searchTerm));
        const matchesRole = roleValue === '' || role === roleValue;
        const matchesStatus = statusValue === '' || status === statusValue;
        const matchesAccess = accessValue === '' || access === accessValue;

        const isVisible = matchesSearch && matchesRole && matchesStatus && matchesAccess;

        card.classList.toggle('hidden', !isVisible);

        if (isVisible) {
          cardVisibleCount++;
        }
      });

      // Show/hide no results messages
      if (noResultsMessage) {
        noResultsMessage.classList.toggle('hidden', tableVisibleCount > 0);
      }
      if (noResultsMessageCards) {
        noResultsMessageCards.classList.toggle('hidden', cardVisibleCount > 0);
      }
    }
  });
</script>

{% endblock body %}